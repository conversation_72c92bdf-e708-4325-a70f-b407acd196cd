#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏛️ 简化机构级别验证 - 专注核心修复验证
严格按照修复质量保证检查清单执行验证
"""

import asyncio
import logging
import time
import sys
import os
import json
from typing import Dict, List, Any, Tuple
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SimplifiedInstitutionalValidator:
    """🏛️ 简化机构级别验证器"""
    
    def __init__(self):
        self.results = []
        self.start_time = time.time()
    
    async def execute_core_validation(self) -> Dict[str, Any]:
        """执行核心修复验证"""
        logger.info("🏛️ 开始简化机构级别验证")
        logger.info("=" * 60)
        
        # 核心修复验证
        await self._test_all_core_fixes()
        
        # 生成最终报告
        return self._generate_final_report()
    
    async def _test_all_core_fixes(self):
        """测试所有核心修复"""
        
        # 1. 测试Bug修复验证
        logger.info("📋 测试Bug修复 (Bug5-7)")
        await self._test_bug5_error_handler_stats()
        await self._test_bug6_websocket_null_protection()  
        await self._test_bug7_timestamp_sync_status()
        
        # 2. 测试问题修复验证  
        logger.info("📋 测试问题修复 (问题7-9)")
        await self._test_problem7_api_config_consistency()
        await self._test_problem8_contract_info_retry()
        await self._test_problem9_config_validation()
        
        # 3. 测试方案实施验证
        logger.info("📋 测试方案实施 (方案1-3)")
        await self._test_solution1_api_optimization()
        await self._test_solution2_gate_validation()
        await self._test_solution3_websocket_pool()
        
        # 4. 数据阻塞问题验证
        logger.info("📋 测试数据阻塞问题解决")
        await self._test_data_blocking_resolution()
    
    async def _test_bug5_error_handler_stats(self):
        """测试Bug5: 错误处理器统计计算修复"""
        try:
            from websocket.error_handler import get_unified_error_handler
            
            error_handler = get_unified_error_handler()
            
            # 测试统计计算不会出现TypeError
            stats = error_handler.get_stats()
            
            success = all([
                isinstance(stats, dict),
                "total_errors" in stats,
                "recovery_rate" in stats,
                isinstance(stats["recovery_rate"], (int, float))
            ])
            
            self.results.append({
                "test": "Bug5_错误处理器统计计算修复",
                "success": success,
                "message": "✅ Bug5修复验证通过" if success else "❌ Bug5修复验证失败"
            })
            logger.info(f"   - {'✅' if success else '❌'} Bug5: 错误处理器统计计算修复")
            
        except Exception as e:
            self.results.append({
                "test": "Bug5_错误处理器统计计算修复", 
                "success": False,
                "message": f"❌ Bug5测试异常: {e}"
            })
            logger.error(f"   - ❌ Bug5测试异常: {e}")
    
    async def _test_bug6_websocket_null_protection(self):
        """测试Bug6: WebSocket空指针异常修复"""
        try:
            from websocket.ws_client import BaseWebSocketClient
            
            # 创建客户端并模拟空指针情况
            client = BaseWebSocketClient("test", "wss://test.com")
            client.ws = None
            
            # 测试关闭连接不会出现空指针异常
            await client.close_connection()
            
            success = True  # 如果没有异常就说明修复成功
            
            self.results.append({
                "test": "Bug6_WebSocket空指针异常修复",
                "success": success,
                "message": "✅ Bug6修复验证通过" if success else "❌ Bug6修复验证失败"
            })
            logger.info(f"   - ✅ Bug6: WebSocket空指针异常修复")
            
        except Exception as e:
            self.results.append({
                "test": "Bug6_WebSocket空指针异常修复",
                "success": False, 
                "message": f"❌ Bug6测试异常: {e}"
            })
            logger.error(f"   - ❌ Bug6测试异常: {e}")
    
    async def _test_bug7_timestamp_sync_status(self):
        """测试Bug7: 时间戳同步状态一致性修复"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            processor = get_timestamp_processor("test")
            processor.time_synced = True
            processor.time_offset = 50
            
            # 测试状态报告一致性
            sync_status = processor.get_sync_status()
            
            success = all([
                sync_status.get("time_synced") == True,
                "offset_status" in sync_status,
                isinstance(sync_status.get("time_offset_ms"), (int, float))
            ])
            
            self.results.append({
                "test": "Bug7_时间戳同步状态一致性修复",
                "success": success,
                "message": "✅ Bug7修复验证通过" if success else "❌ Bug7修复验证失败"
            })
            logger.info(f"   - {'✅' if success else '❌'} Bug7: 时间戳同步状态一致性修复")
            
        except Exception as e:
            self.results.append({
                "test": "Bug7_时间戳同步状态一致性修复",
                "success": False,
                "message": f"❌ Bug7测试异常: {e}"
            })
            logger.error(f"   - ❌ Bug7测试异常: {e}")
    
    async def _test_problem7_api_config_consistency(self):
        """测试问题7: API调用优化器配置一致性修复"""
        try:
            from core.api_call_optimizer import get_api_call_optimizer
            
            optimizer = get_api_call_optimizer()
            
            # 测试OKX配置一致性
            okx_limits = optimizer.get_exchange_limits("okx")
            
            success = all([
                isinstance(okx_limits, dict),
                "requests_per_second" in okx_limits,
                okx_limits["requests_per_second"] == 1.5  # 配置已统一
            ])
            
            self.results.append({
                "test": "问题7_API调用优化器配置一致性修复",
                "success": success, 
                "message": "✅ 问题7修复验证通过" if success else "❌ 问题7修复验证失败"
            })
            logger.info(f"   - {'✅' if success else '❌'} 问题7: API调用优化器配置一致性修复")
            
        except Exception as e:
            self.results.append({
                "test": "问题7_API调用优化器配置一致性修复",
                "success": False,
                "message": f"❌ 问题7测试异常: {e}"
            })
            logger.error(f"   - ❌ 问题7测试异常: {e}")
    
    async def _test_problem8_contract_info_retry(self):
        """测试问题8: 合约信息获取重试机制修复"""
        try:
            from utils.margin_calculator import get_margin_calculator
            
            calculator = get_margin_calculator()
            
            # 测试重试机制存在
            success = all([
                hasattr(calculator, '_get_contract_info_with_retry'),
                hasattr(calculator, 'diagnose_margin_issues')
            ])
            
            self.results.append({
                "test": "问题8_合约信息获取重试机制修复",
                "success": success,
                "message": "✅ 问题8修复验证通过" if success else "❌ 问题8修复验证失败"
            })
            logger.info(f"   - {'✅' if success else '❌'} 问题8: 合约信息获取重试机制修复")
            
        except Exception as e:
            self.results.append({
                "test": "问题8_合约信息获取重试机制修复",
                "success": False,
                "message": f"❌ 问题8测试异常: {e}"
            })
            logger.error(f"   - ❌ 问题8测试异常: {e}")
    
    async def _test_problem9_config_validation(self):
        """测试问题9: 交易对配置验证修复"""
        try:
            from config.universal_config_validator import get_universal_config_validator
            
            validator = get_universal_config_validator()
            
            # 测试跨交易所验证功能
            test_symbols = ["BTC-USDT", "ETH-USDT"]
            validation_result = await validator.validate_symbols_across_exchanges(test_symbols)
            
            success = all([
                isinstance(validation_result, dict),
                "validation_results" in validation_result,
                "summary" in validation_result
            ])
            
            self.results.append({
                "test": "问题9_通用配置验证器修复",
                "success": success,
                "message": "✅ 问题9修复验证通过" if success else "❌ 问题9修复验证失败"
            })
            logger.info(f"   - {'✅' if success else '❌'} 问题9: 通用配置验证器修复")
            
        except Exception as e:
            self.results.append({
                "test": "问题9_通用配置验证器修复",
                "success": False,
                "message": f"❌ 问题9测试异常: {e}"
            })
            logger.error(f"   - ❌ 问题9测试异常: {e}")
    
    async def _test_solution1_api_optimization(self):
        """测试方案1: OKX API限速智能优化系统"""
        try:
            from core.api_call_optimizer import get_api_call_optimizer
            
            optimizer = get_api_call_optimizer()
            
            # 测试缓存和优先级管理机制
            success = all([
                hasattr(optimizer, 'api_cache'),
                hasattr(optimizer, 'priority_manager'),
                hasattr(optimizer, 'get_exchange_limits')
            ])
            
            self.results.append({
                "test": "方案1_OKX_API限速智能优化系统",
                "success": success,
                "message": "✅ 方案1验证通过" if success else "❌ 方案1验证失败"
            })
            logger.info(f"   - {'✅' if success else '❌'} 方案1: OKX API限速智能优化系统")
            
        except Exception as e:
            self.results.append({
                "test": "方案1_OKX_API限速智能优化系统",
                "success": False,
                "message": f"❌ 方案1测试异常: {e}"
            })
            logger.error(f"   - ❌ 方案1测试异常: {e}")
    
    async def _test_solution2_gate_validation(self):
        """测试方案2: Gate.io智能预验证系统"""
        try:
            from core.gate_intelligent_pre_validator import get_gate_intelligent_pre_validator
            
            validator = get_gate_intelligent_pre_validator()
            
            # 测试智能验证功能
            validation_result = await validator.intelligent_validate("BTC-USDT")
            
            success = all([
                isinstance(validation_result, dict),
                "symbol" in validation_result,
                "overall_score" in validation_result
            ])
            
            self.results.append({
                "test": "方案2_Gate.io智能预验证系统", 
                "success": success,
                "message": "✅ 方案2验证通过" if success else "❌ 方案2验证失败"
            })
            logger.info(f"   - {'✅' if success else '❌'} 方案2: Gate.io智能预验证系统")
            
        except Exception as e:
            self.results.append({
                "test": "方案2_Gate.io智能预验证系统",
                "success": False,
                "message": f"❌ 方案2测试异常: {e}"
            })
            logger.error(f"   - ❌ 方案2测试异常: {e}")
    
    async def _test_solution3_websocket_pool(self):
        """测试方案3: WebSocket连接池管理修复"""
        try:
            from websocket.unified_websocket_pool_manager import get_unified_websocket_pool_manager
            
            pool_manager = get_unified_websocket_pool_manager()
            
            # 测试连接池功能
            pool_status = await pool_manager.get_pool_status()
            
            success = all([
                isinstance(pool_status, dict),
                hasattr(pool_manager, 'register_websocket_client'),
                hasattr(pool_manager, 'handle_client_reconnect')
            ])
            
            self.results.append({
                "test": "方案3_WebSocket连接池管理修复",
                "success": success,
                "message": "✅ 方案3验证通过" if success else "❌ 方案3验证失败"
            })
            logger.info(f"   - {'✅' if success else '❌'} 方案3: WebSocket连接池管理修复")
            
        except Exception as e:
            self.results.append({
                "test": "方案3_WebSocket连接池管理修复",
                "success": False,
                "message": f"❌ 方案3测试异常: {e}"
            })
            logger.error(f"   - ❌ 方案3测试异常: {e}")
    
    async def _test_data_blocking_resolution(self):
        """测试数据阻塞问题解决验证"""
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            processor = get_timestamp_processor("test")
            
            # 测试数据新鲜度检查
            current_time = int(time.time() * 1000)
            fresh_data = {"ts": current_time}
            
            # 获取处理后的时间戳
            fresh_timestamp = processor.get_synced_timestamp(fresh_data)
            
            # 验证新鲜度处理
            is_fresh, age = processor.validate_timestamp_freshness(fresh_timestamp)
            
            success = all([
                isinstance(fresh_timestamp, int),
                isinstance(age, (int, float)),
                age < 5000  # 年龄小于5秒
            ])
            
            self.results.append({
                "test": "数据阻塞问题解决验证",
                "success": success,
                "message": "✅ 数据阻塞问题已完全解决" if success else "❌ 数据阻塞问题未完全解决"
            })
            logger.info(f"   - {'✅' if success else '❌'} 数据阻塞问题解决验证")
            
        except Exception as e:
            self.results.append({
                "test": "数据阻塞问题解决验证",
                "success": False,
                "message": f"❌ 数据阻塞验证异常: {e}"
            })
            logger.error(f"   - ❌ 数据阻塞验证异常: {e}")
    
    def _generate_final_report(self) -> Dict[str, Any]:
        """生成最终验证报告"""
        total_duration = time.time() - self.start_time
        
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r.get("success", False))
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        overall_success = success_rate == 100.0
        
        # 输出报告
        logger.info("=" * 60)
        logger.info("🏛️ 简化机构级别验证报告")
        logger.info("=" * 60)
        logger.info(f"📊 验证摘要:")
        logger.info(f"   验证耗时: {total_duration:.3f}秒")
        logger.info(f"   总体成功率: {success_rate:.1f}%")
        logger.info(f"   机构级别状态: {'✅ PASSED' if overall_success else '❌ FAILED'}")
        logger.info(f"   数据阻塞问题: {'✅ 已解决' if overall_success else '❌ 未解决'}")
        
        logger.info(f"\n📋 测试统计:")
        logger.info(f"   总测试数: {total_tests}")
        logger.info(f"   通过测试: {successful_tests}")
        logger.info(f"   失败测试: {total_tests - successful_tests}")
        
        logger.info(f"\n📄 详细结果:")
        for result in self.results:
            logger.info(f"   - {result['message']}")
        
        final_report = {
            "validation_timestamp": datetime.now().isoformat(),
            "total_duration_seconds": round(total_duration, 3),
            "overall_success": overall_success,
            "overall_success_rate": round(success_rate, 1),
            "total_tests_executed": total_tests,
            "successful_tests": successful_tests,
            "failed_tests": total_tests - successful_tests,
            "test_details": self.results,
            "institutional_grade_status": "✅ PASSED" if overall_success else "❌ FAILED",
            "data_blocking_resolved": overall_success,
            "production_ready": overall_success
        }
        
        # 保存报告
        report_filename = f"simplified_institutional_report_{int(time.time())}.json"
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(final_report, f, indent=2, ensure_ascii=False)
            logger.info(f"\n📄 详细报告已保存: {report_filename}")
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
        
        if overall_success:
            logger.info("🎉 简化机构级别验证通过！系统达到生产级别稳定性")
        else:
            logger.error("❌ 简化机构级别验证失败！需要进一步修复")
        
        logger.info("=" * 60)
        
        return final_report


async def main():
    """主函数"""
    print("🏛️ 启动简化机构级别验证")
    print("=" * 60)
    
    validator = SimplifiedInstitutionalValidator()
    final_report = await validator.execute_core_validation()
    
    return final_report["overall_success"]


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)