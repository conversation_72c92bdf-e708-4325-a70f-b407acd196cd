#!/usr/bin/env python3
"""
🏛️ 机构级别质量验证脚本
确保差价精准性、三交易所一致性、高速性能的前提下进行验证！

验证要求：
- 多交易所一致性
- 系统性能
- 通用性  
- 上下游模块全部联动测试无误

三段进阶验证机制：
① 基础核心测试 - 模块单元功能验证
② 复杂系统级联测试 - 系统协同一致性验证  
③ 生产环境仿真测试 - 确保部署到实盘零失误
"""

import asyncio
import sys
import os
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import json

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class InstitutionalQualityValidator:
    """🏛️ 机构级别质量验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.results = {
            "timestamp": time.time(),
            "phase1_basic_tests": {},
            "phase2_system_integration": {},
            "phase3_production_simulation": {},
            "overall_score": 0,
            "quality_grade": "UNKNOWN",
            "production_ready": False
        }
        
    async def run_full_validation(self) -> Dict[str, Any]:
        """运行完整的机构级别验证"""
        logger.info("🏛️ 开始机构级别质量验证...")
        
        try:
            # Phase 1: 基础核心测试
            await self._phase1_basic_core_tests()
            
            # Phase 2: 复杂系统级联测试
            await self._phase2_system_integration_tests()
            
            # Phase 3: 生产环境仿真测试
            await self._phase3_production_simulation_tests()
            
            # 计算总体评分
            self._calculate_overall_score()
            
            logger.info(f"✅ 机构级别验证完成 - 等级: {self.results['quality_grade']}")
            return self.results
            
        except Exception as e:
            logger.error(f"❌ 验证过程异常: {e}")
            self.results["error"] = str(e)
            return self.results
    
    async def _phase1_basic_core_tests(self):
        """Phase 1: 基础核心测试 - 模块单元功能验证"""
        logger.info("🧪 Phase 1: 基础核心测试")
        
        phase1_results = {}
        
        # 测试1: WebSocket管理器整合验证
        phase1_results["websocket_manager_integration"] = await self._test_websocket_manager_integration()
        
        # 测试2: 重复模块清理验证
        phase1_results["duplicate_modules_cleanup"] = await self._test_duplicate_modules_cleanup()
        
        # 测试3: 错误处理器修复验证
        phase1_results["error_handler_fixes"] = await self._test_error_handler_fixes()
        
        # 测试4: 空指针检查验证
        phase1_results["null_pointer_checks"] = await self._test_null_pointer_checks()
        
        # 测试5: API限速配置一致性
        phase1_results["api_rate_limit_consistency"] = await self._test_api_rate_limit_consistency()
        
        self.results["phase1_basic_tests"] = phase1_results
        
        # 计算Phase 1通过率
        passed_tests = sum(1 for result in phase1_results.values() if result.get("passed", False))
        total_tests = len(phase1_results)
        phase1_score = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"📊 Phase 1 结果: {passed_tests}/{total_tests} 通过 ({phase1_score:.1f}%)")
    
    async def _test_websocket_manager_integration(self) -> Dict[str, Any]:
        """测试WebSocket管理器整合"""
        try:
            # 检查WebSocket管理器是否存在
            ws_manager_path = self.project_root / "websocket" / "ws_manager.py"
            if not ws_manager_path.exists():
                return {"passed": False, "error": "WebSocket管理器文件不存在"}
            
            # 检查是否包含整合的功能
            with open(ws_manager_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            required_features = [
                "ManagedConnection",
                "ConnectionStatus", 
                "create_managed_connection",
                "handle_connection_reconnect",
                "update_connection_status"
            ]
            
            missing_features = []
            for feature in required_features:
                if feature not in content:
                    missing_features.append(feature)
            
            if missing_features:
                return {
                    "passed": False,
                    "error": f"缺少整合功能: {missing_features}"
                }
            
            # 尝试导入WebSocket管理器
            try:
                from websocket.ws_manager import WebSocketManager, get_ws_manager
                manager = WebSocketManager()
                
                # 检查整合的方法是否存在
                integration_methods = [
                    "create_managed_connection",
                    "handle_connection_reconnect", 
                    "update_connection_status"
                ]
                
                for method in integration_methods:
                    if not hasattr(manager, method):
                        return {
                            "passed": False,
                            "error": f"WebSocket管理器缺少方法: {method}"
                        }
                
                return {
                    "passed": True,
                    "message": "WebSocket管理器整合验证通过",
                    "features_count": len(required_features),
                    "methods_count": len(integration_methods)
                }
                
            except ImportError as e:
                return {"passed": False, "error": f"导入WebSocket管理器失败: {e}"}
                
        except Exception as e:
            return {"passed": False, "error": f"测试异常: {e}"}
    
    async def _test_duplicate_modules_cleanup(self) -> Dict[str, Any]:
        """测试重复模块清理"""
        try:
            # 检查重复模块是否已删除
            duplicate_modules = [
                "websocket/unified_connection_pool_manager.py",
                "websocket/unified_websocket_pool_manager.py"
            ]
            
            existing_modules = []
            for module_path in duplicate_modules:
                full_path = self.project_root / module_path
                if full_path.exists():
                    existing_modules.append(module_path)
            
            if existing_modules:
                return {
                    "passed": False,
                    "error": f"重复模块未删除: {existing_modules}"
                }
            
            # 检查引用更新
            files_to_check = [
                "websocket/ws_client.py",
                "websocket/unified_timestamp_processor.py"
            ]
            
            outdated_references = []
            for file_path in files_to_check:
                full_path = self.project_root / file_path
                if full_path.exists():
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否还有对已删除模块的引用
                    if "unified_connection_pool_manager" in content:
                        outdated_references.append(f"{file_path}: unified_connection_pool_manager")
                    if "unified_websocket_pool_manager" in content:
                        outdated_references.append(f"{file_path}: unified_websocket_pool_manager")
            
            if outdated_references:
                return {
                    "passed": False,
                    "error": f"存在过期引用: {outdated_references}"
                }
            
            return {
                "passed": True,
                "message": "重复模块清理验证通过",
                "deleted_modules": len(duplicate_modules),
                "checked_files": len(files_to_check)
            }
            
        except Exception as e:
            return {"passed": False, "error": f"测试异常: {e}"}
    
    async def _test_error_handler_fixes(self) -> Dict[str, Any]:
        """测试错误处理器修复"""
        try:
            error_handler_path = self.project_root / "websocket" / "error_handler.py"
            if not error_handler_path.exists():
                return {"passed": False, "error": "错误处理器文件不存在"}
            
            with open(error_handler_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有Bug5的语法错误
            problematic_patterns = [
                r'sum\(len\(\[.*?\]\)\)',
                r'total_attempts\s*=\s*sum\(len\(',
                r'successful_recoveries\s*=\s*sum\(len\('
            ]
            
            import re
            issues_found = []
            lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                for pattern in problematic_patterns:
                    if re.search(pattern, line):
                        issues_found.append(f"Line {i}: {line.strip()}")
            
            if issues_found:
                return {
                    "passed": False,
                    "error": f"发现Bug5语法错误: {issues_found}"
                }
            
            return {
                "passed": True,
                "message": "错误处理器修复验证通过",
                "checked_patterns": len(problematic_patterns)
            }
            
        except Exception as e:
            return {"passed": False, "error": f"测试异常: {e}"}
    
    async def _test_null_pointer_checks(self) -> Dict[str, Any]:
        """测试空指针检查"""
        try:
            websocket_files = [
                "websocket/ws_client.py",
                "websocket/okx_ws.py",
                "websocket/gate_ws.py", 
                "websocket/bybit_ws.py"
            ]
            
            total_checks = 0
            protected_calls = 0
            
            for file_path in websocket_files:
                full_path = self.project_root / file_path
                if not full_path.exists():
                    continue
                
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                
                for i, line in enumerate(lines, 1):
                    if 'await self.ws.close()' in line:
                        total_checks += 1
                        
                        # 检查前面几行是否有空指针检查
                        context_start = max(0, i-5)
                        context_lines = lines[context_start:i]
                        
                        has_null_check = any(
                            'if self.ws is not None:' in ctx or 
                            'if self.ws:' in ctx or
                            'if hasattr(self, "ws") and self.ws' in ctx
                            for ctx in context_lines
                        )
                        
                        if has_null_check:
                            protected_calls += 1
            
            if total_checks == 0:
                return {"passed": True, "message": "未发现WebSocket关闭调用"}
            
            protection_rate = (protected_calls / total_checks * 100) if total_checks > 0 else 0
            
            return {
                "passed": protection_rate >= 80,  # 至少80%的调用有保护
                "message": f"空指针保护率: {protection_rate:.1f}%",
                "total_calls": total_checks,
                "protected_calls": protected_calls,
                "protection_rate": protection_rate
            }
            
        except Exception as e:
            return {"passed": False, "error": f"测试异常: {e}"}

    async def _test_api_rate_limit_consistency(self) -> Dict[str, Any]:
        """测试API限速配置一致性 - 🔥 增强版：检查多个配置源"""
        try:
            import re

            # 检查API调用优化器配置
            optimizer_path = self.project_root / "core" / "api_call_optimizer.py"
            if not optimizer_path.exists():
                return {"passed": False, "error": "API调用优化器文件不存在"}

            with open(optimizer_path, 'r', encoding='utf-8') as f:
                optimizer_content = f.read()

            # 检查交易所配置一致性
            exchanges = ["okx", "gate", "bybit"]
            rate_limits = {}

            # 1. 检查API调用优化器中的配置
            for exchange in exchanges:
                pattern = rf'"{exchange}":\s*(\d+)'
                match = re.search(pattern, optimizer_content)
                if match:
                    rate_limits[f"optimizer_{exchange}"] = int(match.group(1))

            # 2. 检查各交易所文件中的配置
            exchange_files = {
                "okx": "exchanges/okx_exchange.py",
                "gate": "exchanges/gate_exchange.py",
                "bybit": "exchanges/bybit_exchange.py"
            }

            for exchange, file_path in exchange_files.items():
                full_path = self.project_root / file_path
                if full_path.exists():
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 查找 self.rate_limit = 数字 的配置
                    pattern = r'self\.rate_limit\s*=\s*(\d+)'
                    match = re.search(pattern, content)
                    if match:
                        rate_limits[f"exchange_{exchange}"] = int(match.group(1))

            # 检查所有配置值是否一致
            all_values = list(rate_limits.values())
            if len(set(all_values)) > 1:
                return {
                    "passed": False,
                    "error": f"API限速配置不一致: {rate_limits}"
                }

            # 确保至少找到了一些配置
            if not rate_limits:
                return {"passed": False, "error": "未找到任何API限速配置"}

            return {
                "passed": True,
                "message": "API限速配置一致性验证通过",
                "rate_limits": rate_limits,
                "consistent_value": all_values[0] if all_values else None,
                "sources_checked": len(rate_limits)
            }

        except Exception as e:
            return {"passed": False, "error": f"测试异常: {e}"}

    async def _phase2_system_integration_tests(self):
        """Phase 2: 复杂系统级联测试 - 系统协同一致性验证"""
        logger.info("🔗 Phase 2: 复杂系统级联测试")

        phase2_results = {}

        # 测试1: 三交易所一致性验证
        phase2_results["three_exchange_consistency"] = await self._test_three_exchange_consistency()

        # 测试2: 模块间交互逻辑验证
        phase2_results["module_interaction_logic"] = await self._test_module_interaction_logic()

        self.results["phase2_system_integration"] = phase2_results

        # 计算Phase 2通过率
        passed_tests = sum(1 for result in phase2_results.values() if result.get("passed", False))
        total_tests = len(phase2_results)
        phase2_score = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        logger.info(f"📊 Phase 2 结果: {passed_tests}/{total_tests} 通过 ({phase2_score:.1f}%)")

    async def _test_three_exchange_consistency(self) -> Dict[str, Any]:
        """测试三交易所一致性"""
        try:
            exchanges = ["okx", "gate", "bybit"]
            consistency_score = 0
            total_checks = 0

            # 检查WebSocket客户端一致性
            ws_clients = {
                "okx": "websocket/okx_ws.py",
                "gate": "websocket/gate_ws.py",
                "bybit": "websocket/bybit_ws.py"
            }

            client_methods = {}
            for exchange, file_path in ws_clients.items():
                full_path = self.project_root / file_path
                if full_path.exists():
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 提取方法名
                    import re
                    methods = re.findall(r'def\s+(\w+)\s*\(', content)
                    client_methods[exchange] = set(methods)

            # 计算方法一致性
            if len(client_methods) >= 2:
                all_methods = set()
                for methods in client_methods.values():
                    all_methods.update(methods)

                for method in all_methods:
                    total_checks += 1
                    exchanges_with_method = sum(1 for methods in client_methods.values() if method in methods)
                    if exchanges_with_method == len(client_methods):
                        consistency_score += 1

            consistency_rate = (consistency_score / total_checks * 100) if total_checks > 0 else 0

            return {
                "passed": consistency_rate >= 70,  # 至少70%一致性
                "message": f"三交易所一致性: {consistency_rate:.1f}%",
                "consistency_score": consistency_score,
                "total_checks": total_checks,
                "consistency_rate": consistency_rate
            }

        except Exception as e:
            return {"passed": False, "error": f"测试异常: {e}"}

    async def _test_module_interaction_logic(self) -> Dict[str, Any]:
        """测试模块间交互逻辑"""
        try:
            # 检查关键模块是否存在
            key_modules = [
                "websocket/ws_manager.py",
                "core/api_call_optimizer.py",
                "websocket/error_handler.py"
            ]

            existing_modules = 0
            for module_path in key_modules:
                full_path = self.project_root / module_path
                if full_path.exists():
                    existing_modules += 1

            module_existence_rate = (existing_modules / len(key_modules) * 100)

            return {
                "passed": module_existence_rate >= 80,  # 至少80%的关键模块存在
                "message": f"关键模块存在率: {module_existence_rate:.1f}%",
                "existing_modules": existing_modules,
                "total_modules": len(key_modules),
                "module_existence_rate": module_existence_rate
            }

        except Exception as e:
            return {"passed": False, "error": f"测试异常: {e}"}

    async def _phase3_production_simulation_tests(self):
        """Phase 3: 生产环境仿真测试 - 确保部署到实盘零失误"""
        logger.info("🚀 Phase 3: 生产环境仿真测试")

        phase3_results = {}

        # 测试1: 系统启动完整性
        phase3_results["system_startup_integrity"] = await self._test_system_startup_integrity()

        # 测试2: 配置文件完整性
        phase3_results["configuration_integrity"] = await self._test_configuration_integrity()

        # 测试3: 依赖关系完整性
        phase3_results["dependency_integrity"] = await self._test_dependency_integrity()

        self.results["phase3_production_simulation"] = phase3_results

        # 计算Phase 3通过率
        passed_tests = sum(1 for result in phase3_results.values() if result.get("passed", False))
        total_tests = len(phase3_results)
        phase3_score = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        logger.info(f"📊 Phase 3 结果: {passed_tests}/{total_tests} 通过 ({phase3_score:.1f}%)")

    async def _test_system_startup_integrity(self) -> Dict[str, Any]:
        """测试系统启动完整性"""
        try:
            # 检查主要入口文件
            entry_files = [
                "main.py",
                "app.py",
                "run.py"
            ]

            found_entry = None
            for entry_file in entry_files:
                full_path = self.project_root / entry_file
                if full_path.exists():
                    found_entry = entry_file
                    break

            if not found_entry:
                return {"passed": False, "error": "未找到系统入口文件"}

            # 检查入口文件的导入
            with open(self.project_root / found_entry, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查是否导入了关键模块
            critical_imports = [
                "websocket",
                "core",
                "exchanges"
            ]

            import_score = 0
            for critical_import in critical_imports:
                if critical_import in content:
                    import_score += 1

            import_rate = (import_score / len(critical_imports) * 100)

            return {
                "passed": import_rate >= 60,  # 至少60%的关键导入
                "message": f"系统启动完整性: {import_rate:.1f}%",
                "entry_file": found_entry,
                "import_score": import_score,
                "total_imports": len(critical_imports),
                "import_rate": import_rate
            }

        except Exception as e:
            return {"passed": False, "error": f"测试异常: {e}"}

    async def _test_configuration_integrity(self) -> Dict[str, Any]:
        """测试配置文件完整性"""
        try:
            # 检查配置文件
            config_files = [
                ".env",
                ".env.sample",
                "config.py",
                "settings.py"
            ]

            existing_configs = []
            for config_file in config_files:
                full_path = self.project_root / config_file
                if full_path.exists():
                    existing_configs.append(config_file)

            if not existing_configs:
                return {"passed": False, "error": "未找到配置文件"}

            # 检查配置文件内容
            config_completeness = 0
            total_checks = 0

            for config_file in existing_configs:
                full_path = self.project_root / config_file
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查关键配置项
                key_configs = [
                    "API_KEY",
                    "SECRET_KEY",
                    "EXCHANGE",
                    "WEBSOCKET"
                ]

                for key_config in key_configs:
                    total_checks += 1
                    if key_config in content.upper():
                        config_completeness += 1

            completeness_rate = (config_completeness / total_checks * 100) if total_checks > 0 else 0

            return {
                "passed": completeness_rate >= 50,  # 至少50%的配置完整
                "message": f"配置完整性: {completeness_rate:.1f}%",
                "existing_configs": existing_configs,
                "config_completeness": config_completeness,
                "total_checks": total_checks,
                "completeness_rate": completeness_rate
            }

        except Exception as e:
            return {"passed": False, "error": f"测试异常: {e}"}

    async def _test_dependency_integrity(self) -> Dict[str, Any]:
        """测试依赖关系完整性"""
        try:
            # 检查依赖文件
            dependency_files = [
                "requirements.txt",
                "pyproject.toml",
                "setup.py"
            ]

            found_deps = []
            for dep_file in dependency_files:
                full_path = self.project_root / dep_file
                if full_path.exists():
                    found_deps.append(dep_file)

            if not found_deps:
                return {"passed": False, "error": "未找到依赖文件"}

            # 检查关键依赖
            key_dependencies = [
                "websockets",
                "asyncio",
                "aiohttp",
                "requests"
            ]

            dependency_score = 0
            found_dependencies = set()

            for dep_file in found_deps:
                full_path = self.project_root / dep_file
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()

                for key_dep in key_dependencies:
                    if key_dep in content and key_dep not in found_dependencies:
                        dependency_score += 1
                        found_dependencies.add(key_dep)

            dependency_rate = (dependency_score / len(key_dependencies) * 100)

            return {
                "passed": dependency_rate >= 40,  # 至少40%的关键依赖
                "message": f"依赖完整性: {dependency_rate:.1f}%",
                "found_deps": found_deps,
                "dependency_score": dependency_score,
                "total_dependencies": len(key_dependencies),
                "dependency_rate": dependency_rate
            }

        except Exception as e:
            return {"passed": False, "error": f"测试异常: {e}"}

    def _calculate_overall_score(self):
        """计算总体评分"""
        try:
            # 计算各阶段通过率
            phase1_results = self.results.get("phase1_basic_tests", {})
            phase2_results = self.results.get("phase2_system_integration", {})
            phase3_results = self.results.get("phase3_production_simulation", {})

            # Phase 1 权重: 40%
            phase1_passed = sum(1 for result in phase1_results.values() if result.get("passed", False))
            phase1_total = len(phase1_results) if phase1_results else 1
            phase1_score = (phase1_passed / phase1_total) * 40

            # Phase 2 权重: 35%
            phase2_passed = sum(1 for result in phase2_results.values() if result.get("passed", False))
            phase2_total = len(phase2_results) if phase2_results else 1
            phase2_score = (phase2_passed / phase2_total) * 35

            # Phase 3 权重: 25%
            phase3_passed = sum(1 for result in phase3_results.values() if result.get("passed", False))
            phase3_total = len(phase3_results) if phase3_results else 1
            phase3_score = (phase3_passed / phase3_total) * 25

            # 总分
            overall_score = phase1_score + phase2_score + phase3_score

            # 确定质量等级
            if overall_score >= 90:
                quality_grade = "A+ (机构级别)"
                production_ready = True
            elif overall_score >= 80:
                quality_grade = "A (优秀)"
                production_ready = True
            elif overall_score >= 70:
                quality_grade = "B+ (良好)"
                production_ready = True
            elif overall_score >= 60:
                quality_grade = "B (合格)"
                production_ready = False
            elif overall_score >= 50:
                quality_grade = "C (需改进)"
                production_ready = False
            else:
                quality_grade = "D (不合格)"
                production_ready = False

            self.results["overall_score"] = round(overall_score, 1)
            self.results["quality_grade"] = quality_grade
            self.results["production_ready"] = production_ready

            # 详细评分
            self.results["detailed_scores"] = {
                "phase1_score": round(phase1_score, 1),
                "phase2_score": round(phase2_score, 1),
                "phase3_score": round(phase3_score, 1),
                "phase1_pass_rate": round((phase1_passed / phase1_total) * 100, 1),
                "phase2_pass_rate": round((phase2_passed / phase2_total) * 100, 1),
                "phase3_pass_rate": round((phase3_passed / phase3_total) * 100, 1)
            }

        except Exception as e:
            logger.error(f"❌ 评分计算异常: {e}")
            self.results["overall_score"] = 0
            self.results["quality_grade"] = "ERROR"
            self.results["production_ready"] = False

    def print_detailed_report(self):
        """打印详细报告"""
        print("\n" + "="*80)
        print("🏛️ 机构级别质量验证报告")
        print("="*80)

        print(f"\n📊 总体评分: {self.results['overall_score']}/100")
        print(f"🏆 质量等级: {self.results['quality_grade']}")
        print(f"🚀 生产就绪: {'✅ 是' if self.results['production_ready'] else '❌ 否'}")

        if "detailed_scores" in self.results:
            scores = self.results["detailed_scores"]
            print(f"\n📈 详细评分:")
            print(f"   Phase 1 (基础核心): {scores['phase1_score']}/40 ({scores['phase1_pass_rate']}%)")
            print(f"   Phase 2 (系统集成): {scores['phase2_score']}/35 ({scores['phase2_pass_rate']}%)")
            print(f"   Phase 3 (生产仿真): {scores['phase3_score']}/25 ({scores['phase3_pass_rate']}%)")

        # 打印各阶段详细结果
        for phase_name, phase_results in [
            ("Phase 1: 基础核心测试", self.results.get("phase1_basic_tests", {})),
            ("Phase 2: 系统集成测试", self.results.get("phase2_system_integration", {})),
            ("Phase 3: 生产仿真测试", self.results.get("phase3_production_simulation", {}))
        ]:
            if phase_results:
                print(f"\n🧪 {phase_name}:")
                for test_name, result in phase_results.items():
                    status = "✅" if result.get("passed", False) else "❌"
                    message = result.get("message", result.get("error", "无详细信息"))
                    print(f"   {status} {test_name}: {message}")

        print("\n" + "="*80)

async def main():
    """主函数"""
    print("🏛️ 启动机构级别质量验证...")

    validator = InstitutionalQualityValidator()
    results = await validator.run_full_validation()

    # 打印详细报告
    validator.print_detailed_report()

    # 保存结果到文件
    timestamp = int(time.time())
    results_file = f"validation_results_{timestamp}.json"

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"\n💾 验证结果已保存到: {results_file}")

    # 返回退出码
    return 0 if results.get("production_ready", False) else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
