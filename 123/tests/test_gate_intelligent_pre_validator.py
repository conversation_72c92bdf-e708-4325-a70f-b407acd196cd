#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 方案2验证脚本: Gate.io交易对智能预验证系统测试

验证目标：
1. Gate.io智能预验证系统功能完整性
2. 缓存机制有效性
3. 智能评分算法准确性  
4. 推荐引擎实用性
5. 性能优化效果
"""

import asyncio
import time
import sys
import os
import logging
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_gate_intelligent_pre_validator():
    """
    🔥 测试Gate.io智能预验证系统
    """
    try:
        print("🚀 开始测试Gate.io智能预验证系统...")
        
        # 🔥 导入必要模块
        from core.gate_intelligent_pre_validator import (
            get_gate_intelligent_pre_validator,
            validate_gate_config_intelligent,
            ValidationScore,
            GateValidationResult,
            GateIntelligentReport
        )
        
        # 🔥 导入Gate.io交易所
        from exchanges.gate_exchange import GateExchange
        
        # 🔥 获取验证器实例
        validator = get_gate_intelligent_pre_validator()
        print("✅ Gate.io智能预验证器实例创建成功")
        
        # 🔥 创建模拟Gate.io交易所实例
        gate_exchange = MockGateExchange()
        print("✅ 模拟Gate.io交易所创建成功")
        
        # 🔥 测试1: 单个交易对智能验证
        print("\n📋 测试1: 单个交易对智能验证")
        test_symbols = ["BTC-USDT", "ETH-USDT", "INVALID-PAIR"]
        
        for symbol in test_symbols:
            print(f"\n🔍 验证交易对: {symbol}")
            start_time = time.time()
            
            result = await validator.validate_symbol_intelligent(symbol, gate_exchange)
            
            validation_time = time.time() - start_time
            print(f"   验证结果: {result.grade.value} ({result.score}分)")
            print(f"   现货支持: {result.spot_supported}")
            print(f"   期货支持: {result.futures_supported}")
            print(f"   验证耗时: {validation_time:.3f}秒")
            print(f"   缓存状态: {'命中' if result.cached else '未命中'}")
            
            if result.recommendations:
                print(f"   推荐建议: {result.recommendations[0]}")
            
            if result.alternatives:
                print(f"   替代方案: {', '.join(result.alternatives[:3])}")
        
        # 🔥 测试2: 批量智能验证
        print("\n📋 测试2: 批量智能验证")
        batch_symbols = [
            "BTC-USDT", "ETH-USDT", "DOGE-USDT", "ADA-USDT", 
            "MATIC-USDT", "DOT-USDT", "INVALID-PAIR1", "INVALID-PAIR2"
        ]
        
        print(f"🔍 批量验证 {len(batch_symbols)} 个交易对...")
        batch_start_time = time.time()
        
        report = await validator.validate_batch_intelligent(batch_symbols, gate_exchange)
        
        batch_time = time.time() - batch_start_time
        print(f"✅ 批量验证完成，耗时: {batch_time:.3f}秒")
        
        # 🔥 打印智能报告
        validator.print_intelligent_report(report)
        
        # 🔥 测试3: 缓存机制验证
        print("\n📋 测试3: 缓存机制验证")
        cache_test_symbol = "BTC-USDT"
        
        # 第一次验证（无缓存）
        print(f"🔍 第一次验证 {cache_test_symbol}（无缓存）")
        start_time = time.time()
        result1 = await validator.validate_symbol_intelligent(cache_test_symbol, gate_exchange)
        time1 = time.time() - start_time
        print(f"   耗时: {time1:.3f}秒, 缓存: {result1.cached}")
        
        # 第二次验证（应该命中缓存）
        print(f"🔍 第二次验证 {cache_test_symbol}（应该命中缓存）")
        start_time = time.time()
        result2 = await validator.validate_symbol_intelligent(cache_test_symbol, gate_exchange)
        time2 = time.time() - start_time
        print(f"   耗时: {time2:.3f}秒, 缓存: {result2.cached}")
        
        # 🔥 验证缓存效果
        if result2.cached and time2 < time1:
            print(f"✅ 缓存机制有效: 第二次验证快 {((time1 - time2) / time1 * 100):.1f}%")
        else:
            print(f"⚠️ 缓存机制可能有问题")
        
        # 🔥 测试4: 评分算法验证
        print("\n📋 测试4: 评分算法验证")
        
        # 创建不同质量的测试数据
        test_cases = [
            {
                "symbol": "EXCELLENT-PAIR",
                "spot_supported": True,
                "futures_supported": True,
                "expected_grade": ValidationScore.EXCELLENT
            },
            {
                "symbol": "GOOD-PAIR", 
                "spot_supported": True,
                "futures_supported": True,
                "expected_grade": ValidationScore.GOOD
            },
            {
                "symbol": "POOR-PAIR",
                "spot_supported": True,
                "futures_supported": False,
                "expected_grade": ValidationScore.POOR
            },
            {
                "symbol": "UNSUPPORTED-PAIR",
                "spot_supported": False,
                "futures_supported": False,
                "expected_grade": ValidationScore.UNSUPPORTED
            }
        ]
        
        scoring_accuracy = 0
        for test_case in test_cases:
            # 设置模拟交易所的返回数据
            gate_exchange.set_mock_data(test_case["symbol"], {
                "spot_supported": test_case["spot_supported"],
                "futures_supported": test_case["futures_supported"]
            })
            
            result = await validator.validate_symbol_intelligent(test_case["symbol"], gate_exchange)
            expected_grade = test_case["expected_grade"]
            
            print(f"   {test_case['symbol']}: 预期{expected_grade.value}, 实际{result.grade.value}, 评分{result.score}")
            
            # 检查评分是否合理
            if expected_grade == ValidationScore.EXCELLENT and result.score >= 95:
                scoring_accuracy += 1
            elif expected_grade == ValidationScore.GOOD and 80 <= result.score < 95:
                scoring_accuracy += 1
            elif expected_grade == ValidationScore.POOR and 40 <= result.score < 60:
                scoring_accuracy += 1
            elif expected_grade == ValidationScore.UNSUPPORTED and result.score < 40:
                scoring_accuracy += 1
        
        scoring_accuracy_rate = scoring_accuracy / len(test_cases) * 100
        print(f"✅ 评分算法准确率: {scoring_accuracy_rate:.1f}%")
        
        # 🔥 测试5: 性能统计验证
        print("\n📋 测试5: 性能统计验证")
        print(f"性能统计:")
        for key, value in validator.performance_stats.items():
            print(f"   {key}: {value}")
        
        # 🔥 测试结果汇总
        print("\n📊 测试结果汇总:")
        tests_passed = 0
        total_tests = 5
        
        # 检查各项测试结果
        if result1 and result2:
            tests_passed += 1
            print("✅ 单个交易对验证: 通过")
        else:
            print("❌ 单个交易对验证: 失败")
        
        if report and report.validated_symbols > 0:
            tests_passed += 1
            print("✅ 批量验证: 通过")
        else:
            print("❌ 批量验证: 失败")
        
        if result2.cached:
            tests_passed += 1
            print("✅ 缓存机制: 通过")
        else:
            print("❌ 缓存机制: 失败")
        
        if scoring_accuracy_rate >= 75:
            tests_passed += 1
            print("✅ 评分算法: 通过")
        else:
            print("❌ 评分算法: 失败")
        
        if validator.performance_stats["total_validations"] > 0:
            tests_passed += 1
            print("✅ 性能统计: 通过")
        else:
            print("❌ 性能统计: 失败")
        
        # 🔥 最终评估
        success_rate = tests_passed / total_tests * 100
        print(f"\n🏆 总体测试结果: {tests_passed}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("✅ Gate.io智能预验证系统测试通过！")
            return True
        else:
            print("❌ Gate.io智能预验证系统测试失败！")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        import traceback
        traceback.print_exc()
        return False


class MockGateExchange:
    """
    🔥 模拟Gate.io交易所（用于测试）
    """
    
    def __init__(self):
        self.mock_data = {}
        
        # 🔥 默认现货交易对数据
        self.default_spot_pairs = [
            {
                "id": "BTC_USDT",
                "base": "BTC",
                "quote": "USDT",
                "fee": "0.002",
                "min_base_amount": "0.00001",
                "min_quote_amount": "5.0",
                "amount_precision": 8,
                "precision": 2,
                "trade_status": "tradable"
            },
            {
                "id": "ETH_USDT",
                "base": "ETH", 
                "quote": "USDT",
                "fee": "0.002",
                "min_base_amount": "0.0001",
                "min_quote_amount": "5.0",
                "amount_precision": 8,
                "precision": 2,
                "trade_status": "tradable"
            },
            {
                "id": "DOGE_USDT",
                "base": "DOGE",
                "quote": "USDT", 
                "fee": "0.002",
                "min_base_amount": "1.0",
                "min_quote_amount": "5.0",
                "amount_precision": 8,
                "precision": 6,
                "trade_status": "tradable"
            },
            {
                "id": "EXCELLENT_PAIR",
                "base": "EXCELLENT",
                "quote": "USDT",
                "fee": "0.001",
                "min_base_amount": "0.0001",
                "min_quote_amount": "1.0",
                "amount_precision": 8,
                "precision": 8,
                "trade_status": "tradable"
            },
            {
                "id": "GOOD_PAIR",
                "base": "GOOD",
                "quote": "USDT",
                "fee": "0.002",
                "min_base_amount": "0.001",
                "min_quote_amount": "5.0",
                "amount_precision": 6,
                "precision": 4,
                "trade_status": "tradable"
            },
            {
                "id": "POOR_PAIR",
                "base": "POOR",
                "quote": "USDT",
                "fee": "0.005",
                "min_base_amount": "1.0",
                "min_quote_amount": "50.0",
                "amount_precision": 4,
                "precision": 2,
                "trade_status": "buyable"  # 只能买入
            }
        ]
        
        # 🔥 默认期货合约数据
        self.default_futures_contracts = [
            {
                "name": "BTC_USDT",
                "type": "direct",
                "quanto_multiplier": "0.0001",
                "leverage_min": "1",
                "leverage_max": "125",
                "maintenance_rate": "0.004",
                "mark_type": "fair_price",
                "last_price": "45000.0",
                "mark_price": "45001.0",
                "index_price": "45000.5",
                "funding_rate": "0.0001",
                "funding_interval": 28800,
                "order_size_min": 1,
                "order_size_max": 1000000,
                "in_delisting": False
            },
            {
                "name": "ETH_USDT",
                "type": "direct",
                "quanto_multiplier": "0.001",
                "leverage_min": "1",
                "leverage_max": "100",
                "maintenance_rate": "0.005",
                "mark_type": "fair_price", 
                "last_price": "3000.0",
                "mark_price": "3001.0",
                "index_price": "3000.5",
                "funding_rate": "0.0001",
                "funding_interval": 28800,
                "order_size_min": 1,
                "order_size_max": 1000000,
                "in_delisting": False
            },
            {
                "name": "EXCELLENT_PAIR",
                "type": "direct",
                "quanto_multiplier": "0.0001",
                "leverage_min": "1",
                "leverage_max": "125",
                "maintenance_rate": "0.003",
                "mark_type": "fair_price",
                "last_price": "100.0",
                "mark_price": "100.0",
                "index_price": "100.0",
                "funding_rate": "0.0001",
                "funding_interval": 28800,
                "order_size_min": 1,
                "order_size_max": 1000000,
                "in_delisting": False
            },
            {
                "name": "GOOD_PAIR",
                "type": "direct",
                "quanto_multiplier": "0.001",
                "leverage_min": "1",
                "leverage_max": "50",
                "maintenance_rate": "0.008",
                "mark_type": "fair_price",
                "last_price": "10.0",
                "mark_price": "10.1",
                "index_price": "10.0",
                "funding_rate": "0.0001", 
                "funding_interval": 28800,
                "order_size_min": 10,
                "order_size_max": 100000,
                "in_delisting": False
            }
        ]
    
    def set_mock_data(self, symbol: str, data: Dict):
        """设置特定交易对的模拟数据"""
        self.mock_data[symbol] = data
    
    async def get_currency_pairs(self):
        """模拟获取现货交易对"""
        await asyncio.sleep(0.1)  # 模拟API延迟
        return self.default_spot_pairs
    
    async def get_futures_contracts(self, settle: str = "usdt"):
        """模拟获取期货合约"""
        await asyncio.sleep(0.1)  # 模拟API延迟
        return self.default_futures_contracts


async def main():
    """主函数"""
    try:
        print("🔥 开始Gate.io智能预验证系统综合测试")
        print("="*60)
        
        success = await test_gate_intelligent_pre_validator()
        
        print("="*60)
        if success:
            print("🎉 所有测试通过！Gate.io智能预验证系统运行正常")
            return 0
        else:
            print("💥 测试失败！请检查Gate.io智能预验证系统")
            return 1
            
    except Exception as e:
        print(f"❌ 测试运行异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    
    # 运行测试
    result = asyncio.run(main())
    sys.exit(result)