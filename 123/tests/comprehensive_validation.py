#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 综合验证脚本 - 验证所有方案3修复和系统完整性

验证范围：
1. Bug5-7: 错误处理器、WebSocket空指针、时间戳同步
2. 问题7-9: API配置一致性、合约信息获取、配置验证
3. 方案1-3: OKX API优化、Gate.io预验证、WebSocket连接池管理

设计原则：
- 全面覆盖所有修复点
- 模拟真实使用场景
- 提供详细的验证报告
- 自动化测试和故障恢复验证
"""

import asyncio
import sys
import os
import time
import logging
from pathlib import Path
from typing import Dict, Any, List, Tuple
import json
import traceback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ComprehensiveValidator:
    """🔥 综合验证器 - 验证所有修复"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validation_results = {
            "bug_fixes": {},      # Bug修复验证
            "problem_fixes": {},  # 问题修复验证
            "solution_implementations": {},  # 方案实施验证
            "integration_tests": {},  # 集成测试
            "performance_tests": {}   # 性能测试
        }
        self.overall_success = True
        
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """🔥 运行综合验证"""
        try:
            self.logger.info("🚀 开始综合验证 - 验证所有修复和方案实施")
            start_time = time.time()
            
            # 🔥 阶段1: 验证Bug修复 (Bug5-7)
            await self._validate_bug_fixes()
            
            # 🔥 阶段2: 验证问题修复 (问题7-9)
            await self._validate_problem_fixes()
            
            # 🔥 阶段3: 验证方案实施 (方案1-3)
            await self._validate_solution_implementations()
            
            # 🔥 阶段4: 集成测试
            await self._run_integration_tests()
            
            # 🔥 阶段5: 性能测试
            await self._run_performance_tests()
            
            # 🔥 生成最终报告
            validation_time = time.time() - start_time
            final_report = self._generate_final_report(validation_time)
            
            self.logger.info(f"✅ 综合验证完成，耗时: {validation_time:.2f}秒")
            return final_report
            
        except Exception as e:
            self.logger.error(f"❌ 综合验证异常: {e}")
            traceback.print_exc()
            self.overall_success = False
            return {"error": str(e), "success": False}
    
    async def _validate_bug_fixes(self):
        """🔥 验证Bug修复 (Bug5-7)"""
        self.logger.info("📋 阶段1: 验证Bug修复 (Bug5-7)")
        
        # Bug5: 错误处理器统计计算错误
        await self._validate_bug5_error_handler_fix()
        
        # Bug6: WebSocket连接空指针异常
        await self._validate_bug6_websocket_null_pointer_fix()
        
        # Bug7: 时间戳同步状态不一致
        await self._validate_bug7_timestamp_sync_fix()
        
    async def _validate_bug5_error_handler_fix(self):
        """🔥 验证Bug5: 错误处理器统计计算错误修复"""
        try:
            self.logger.info("🔍 验证Bug5: 错误处理器统计计算错误修复")
            
            # 检查错误处理器模块是否存在
            try:
                from websocket.error_handler import get_unified_error_handler
                error_handler = get_unified_error_handler()
                
                # 模拟错误事件
                test_error = Exception("测试错误")
                await error_handler.handle_error("test_exchange", test_error)
                
                # 检查统计计算是否正确
                stats = error_handler.get_stats()
                
                # 验证统计字段存在且为数字
                required_fields = ["total_errors", "recovery_rate", "avg_recovery_time"]
                for field in required_fields:
                    if field not in stats:
                        raise ValueError(f"统计字段缺失: {field}")
                    
                    if not isinstance(stats[field], (int, float)):
                        raise ValueError(f"统计字段类型错误: {field}")
                
                self.validation_results["bug_fixes"]["bug5"] = {
                    "status": "PASSED",
                    "message": "错误处理器统计计算修复验证成功",
                    "details": f"统计字段正常: {list(stats.keys())}"
                }
                self.logger.info("✅ Bug5验证通过")
                
            except ImportError:
                self.validation_results["bug_fixes"]["bug5"] = {
                    "status": "SKIPPED",
                    "message": "错误处理器模块不可用",
                    "details": "模块导入失败，可能是可选组件"
                }
                self.logger.warning("⚠️ Bug5验证跳过: 错误处理器模块不可用")
                
        except Exception as e:
            self.validation_results["bug_fixes"]["bug5"] = {
                "status": "FAILED",
                "message": f"Bug5验证失败: {e}",
                "details": traceback.format_exc()
            }
            self.logger.error(f"❌ Bug5验证失败: {e}")
            self.overall_success = False
    
    async def _validate_bug6_websocket_null_pointer_fix(self):
        """🔥 验证Bug6: WebSocket连接空指针异常修复"""
        try:
            self.logger.info("🔍 验证Bug6: WebSocket连接空指针异常修复")
            
            # 创建模拟WebSocket客户端
            from websocket.ws_client import WebSocketClient
            
            class MockWebSocketClient(WebSocketClient):
                def get_ws_url(self):
                    return "ws://test.example.com"
                    
                async def subscribe_channels(self):
                    return True
                    
                async def handle_message(self, message):
                    pass
                    
                async def send_heartbeat(self):
                    return True
            
            # 测试空指针保护
            client = MockWebSocketClient("TEST")
            client.ws = None  # 模拟空连接
            
            # 测试关闭连接的空指针保护
            try:
                await client.close()  # 应该不会抛出空指针异常
                
                self.validation_results["bug_fixes"]["bug6"] = {
                    "status": "PASSED",
                    "message": "WebSocket空指针异常修复验证成功",
                    "details": "空连接关闭操作安全执行"
                }
                self.logger.info("✅ Bug6验证通过")
                
            except AttributeError as e:
                if "'NoneType' object has no attribute" in str(e):
                    self.validation_results["bug_fixes"]["bug6"] = {
                        "status": "FAILED",
                        "message": f"仍存在空指针异常: {e}",
                        "details": "WebSocket空指针保护不完整"
                    }
                    self.logger.error(f"❌ Bug6验证失败: 仍存在空指针异常")
                    self.overall_success = False
                else:
                    raise
                    
        except Exception as e:
            self.validation_results["bug_fixes"]["bug6"] = {
                "status": "FAILED", 
                "message": f"Bug6验证异常: {e}",
                "details": traceback.format_exc()
            }
            self.logger.error(f"❌ Bug6验证异常: {e}")
            self.overall_success = False
    
    async def _validate_bug7_timestamp_sync_fix(self):
        """🔥 验证Bug7: 时间戳同步状态不一致修复"""
        try:
            self.logger.info("🔍 验证Bug7: 时间戳同步状态不一致修复")
            
            # 检查统一时间戳处理器
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            # 测试各交易所的时间戳处理器
            exchanges = ["gate", "okx", "bybit"]
            sync_status_consistent = True
            
            for exchange in exchanges:
                processor = get_timestamp_processor(exchange)
                
                # 检查同步状态一致性
                if hasattr(processor, 'time_synced') and hasattr(processor, 'get_sync_status'):
                    sync_status = processor.get_sync_status()
                    time_synced = processor.time_synced
                    
                    # 验证状态一致性
                    if sync_status == "synced" and not time_synced:
                        sync_status_consistent = False
                        self.logger.warning(f"⚠️ {exchange}时间戳状态不一致")
                        
            if sync_status_consistent:
                self.validation_results["bug_fixes"]["bug7"] = {
                    "status": "PASSED",
                    "message": "时间戳同步状态一致性修复验证成功",
                    "details": f"验证了{len(exchanges)}个交易所的同步状态"
                }
                self.logger.info("✅ Bug7验证通过")
            else:
                self.validation_results["bug_fixes"]["bug7"] = {
                    "status": "FAILED",
                    "message": "时间戳同步状态仍存在不一致",
                    "details": "部分交易所同步状态不匹配"
                }
                self.logger.error("❌ Bug7验证失败")
                self.overall_success = False
                
        except Exception as e:
            self.validation_results["bug_fixes"]["bug7"] = {
                "status": "FAILED",
                "message": f"Bug7验证异常: {e}",
                "details": traceback.format_exc()
            }
            self.logger.error(f"❌ Bug7验证异常: {e}")
            self.overall_success = False
    
    async def _validate_problem_fixes(self):
        """🔥 验证问题修复 (问题7-9)"""
        self.logger.info("📋 阶段2: 验证问题修复 (问题7-9)")
        
        # 问题7: API调用优化器配置不一致
        await self._validate_problem7_api_config_consistency()
        
        # 问题8: 合约信息获取失败导致保证金计算错误
        await self._validate_problem8_contract_info_fix()
        
        # 问题9: 交易对配置验证缺失
        await self._validate_problem9_config_validation_fix()
    
    async def _validate_problem7_api_config_consistency(self):
        """🔥 验证问题7: API调用优化器配置不一致修复"""
        try:
            self.logger.info("🔍 验证问题7: API调用优化器配置不一致修复")
            
            # 检查API调用优化器
            from core.api_call_optimizer import get_api_call_optimizer
            optimizer = get_api_call_optimizer()
            
            # 检查OKX限速配置
            okx_limits = optimizer.get_exchange_limits("okx")
            
            if okx_limits and "requests_per_second" in okx_limits:
                # 验证配置一致性
                requests_per_sec = okx_limits["requests_per_second"]
                
                # 应该是1.5次/秒的安全限制
                if 1.4 <= requests_per_sec <= 1.6:
                    self.validation_results["problem_fixes"]["problem7"] = {
                        "status": "PASSED",
                        "message": "API配置一致性修复验证成功",
                        "details": f"OKX限速配置: {requests_per_sec}次/秒"
                    }
                    self.logger.info("✅ 问题7验证通过")
                else:
                    self.validation_results["problem_fixes"]["problem7"] = {
                        "status": "FAILED",
                        "message": f"API限速配置不正确: {requests_per_sec}次/秒",
                        "details": "应该为1.5次/秒的安全限制"
                    }
                    self.logger.error("❌ 问题7验证失败")
                    self.overall_success = False
            else:
                self.validation_results["problem_fixes"]["problem7"] = {
                    "status": "FAILED",
                    "message": "API限速配置缺失",
                    "details": "未找到OKX限速配置"
                }
                self.logger.error("❌ 问题7验证失败")
                self.overall_success = False
                
        except Exception as e:
            self.validation_results["problem_fixes"]["problem7"] = {
                "status": "FAILED",
                "message": f"问题7验证异常: {e}",
                "details": traceback.format_exc()
            }
            self.logger.error(f"❌ 问题7验证异常: {e}")
            self.overall_success = False
    
    async def _validate_problem8_contract_info_fix(self):
        """🔥 验证问题8: 合约信息获取失败导致保证金计算错误修复"""
        try:
            self.logger.info("🔍 验证问题8: 合约信息获取失败修复")
            
            # 检查保证金计算器
            from utils.margin_calculator import MarginCalculator
            calculator = MarginCalculator()
            
            # 测试合约信息获取的重试机制
            test_symbol = "BTC-USDT"
            
            # 模拟获取合约信息（不实际调用API）
            # 主要验证重试机制的存在
            if hasattr(calculator, '_get_contract_info_with_retry'):
                self.validation_results["problem_fixes"]["problem8"] = {
                    "status": "PASSED",
                    "message": "合约信息获取重试机制修复验证成功",
                    "details": "重试机制已实现"
                }
                self.logger.info("✅ 问题8验证通过")
            else:
                self.validation_results["problem_fixes"]["problem8"] = {
                    "status": "FAILED",
                    "message": "重试机制未实现",
                    "details": "未找到合约信息重试方法"
                }
                self.logger.error("❌ 问题8验证失败")
                self.overall_success = False
                
        except Exception as e:
            self.validation_results["problem_fixes"]["problem8"] = {
                "status": "FAILED",
                "message": f"问题8验证异常: {e}",
                "details": traceback.format_exc()
            }
            self.logger.error(f"❌ 问题8验证异常: {e}")
            self.overall_success = False
    
    async def _validate_problem9_config_validation_fix(self):
        """🔥 验证问题9: 交易对配置验证缺失修复"""
        try:
            self.logger.info("🔍 验证问题9: 交易对配置验证缺失修复")
            
            # 检查通用配置验证器
            from config.universal_config_validator import UniversalConfigValidator
            validator = UniversalConfigValidator()
            
            # 测试配置验证功能
            test_symbols = ["BTC-USDT", "ETH-USDT", "INVALID-PAIR"]
            
            validation_report = await validator.validate_symbols_across_exchanges(test_symbols)
            
            if validation_report and "validation_results" in validation_report:
                valid_count = sum(1 for result in validation_report["validation_results"] 
                                if result.get("is_valid", False))
                
                self.validation_results["problem_fixes"]["problem9"] = {
                    "status": "PASSED",
                    "message": "通用配置验证器修复验证成功",
                    "details": f"验证了{len(test_symbols)}个交易对，{valid_count}个有效"
                }
                self.logger.info("✅ 问题9验证通过")
            else:
                self.validation_results["problem_fixes"]["problem9"] = {
                    "status": "FAILED",
                    "message": "配置验证器功能异常",
                    "details": "验证报告格式不正确"
                }
                self.logger.error("❌ 问题9验证失败")
                self.overall_success = False
                
        except Exception as e:
            self.validation_results["problem_fixes"]["problem9"] = {
                "status": "FAILED",
                "message": f"问题9验证异常: {e}",
                "details": traceback.format_exc()
            }
            self.logger.error(f"❌ 问题9验证异常: {e}")
            self.overall_success = False
    
    async def _validate_solution_implementations(self):
        """🔥 验证方案实施 (方案1-3)"""
        self.logger.info("📋 阶段3: 验证方案实施 (方案1-3)")
        
        # 方案1: OKX API限速智能优化系统
        await self._validate_solution1_okx_api_optimization()
        
        # 方案2: Gate.io交易对智能预验证系统
        await self._validate_solution2_gate_prevalidation()
        
        # 方案3: WebSocket连接池管理缺陷修复
        await self._validate_solution3_websocket_pool_management()
    
    async def _validate_solution1_okx_api_optimization(self):
        """🔥 验证方案1: OKX API限速智能优化系统"""
        try:
            self.logger.info("🔍 验证方案1: OKX API限速智能优化系统")
            
            # 检查API调用优化器增强功能
            from core.api_call_optimizer import get_api_call_optimizer
            optimizer = get_api_call_optimizer()
            
            # 验证缓存机制
            cache_features = []
            if hasattr(optimizer, 'api_cache'):
                cache_features.append("API缓存")
            if hasattr(optimizer, 'websocket_priority_manager'):
                cache_features.append("WebSocket优先级管理")
            if hasattr(optimizer, 'call_deduplication'):
                cache_features.append("调用去重")
            
            if len(cache_features) >= 2:
                self.validation_results["solution_implementations"]["solution1"] = {
                    "status": "PASSED",
                    "message": "OKX API优化系统实施验证成功",
                    "details": f"实现功能: {', '.join(cache_features)}"
                }
                self.logger.info("✅ 方案1验证通过")
            else:
                self.validation_results["solution_implementations"]["solution1"] = {
                    "status": "PARTIAL",
                    "message": "OKX API优化系统部分实施",
                    "details": f"实现功能: {', '.join(cache_features)}"
                }
                self.logger.warning("⚠️ 方案1部分通过")
                
        except Exception as e:
            self.validation_results["solution_implementations"]["solution1"] = {
                "status": "FAILED",
                "message": f"方案1验证异常: {e}",
                "details": traceback.format_exc()
            }
            self.logger.error(f"❌ 方案1验证异常: {e}")
            self.overall_success = False
    
    async def _validate_solution2_gate_prevalidation(self):
        """🔥 验证方案2: Gate.io交易对智能预验证系统"""
        try:
            self.logger.info("🔍 验证方案2: Gate.io智能预验证系统")
            
            # 检查Gate.io智能预验证器
            from core.gate_intelligent_pre_validator import get_gate_intelligent_pre_validator
            validator = get_gate_intelligent_pre_validator()
            
            # 创建模拟Gate.io交易所
            class MockGateExchange:
                async def get_currency_pairs(self):
                    return [{"id": "BTC_USDT", "base": "BTC", "quote": "USDT"}]
                
                async def get_futures_contracts(self, settle="usdt"):
                    return [{"name": "BTC_USDT", "type": "direct"}]
            
            mock_exchange = MockGateExchange()
            
            # 测试智能验证功能
            result = await validator.validate_symbol_intelligent("BTC-USDT", mock_exchange)
            
            if result and hasattr(result, 'score') and hasattr(result, 'grade'):
                self.validation_results["solution_implementations"]["solution2"] = {
                    "status": "PASSED",
                    "message": "Gate.io智能预验证系统实施验证成功",
                    "details": f"验证评分: {result.score}, 等级: {result.grade.value}"
                }
                self.logger.info("✅ 方案2验证通过")
            else:
                self.validation_results["solution_implementations"]["solution2"] = {
                    "status": "FAILED",
                    "message": "智能验证功能异常",
                    "details": "验证结果格式不正确"
                }
                self.logger.error("❌ 方案2验证失败")
                self.overall_success = False
                
        except Exception as e:
            self.validation_results["solution_implementations"]["solution2"] = {
                "status": "FAILED",
                "message": f"方案2验证异常: {e}",
                "details": traceback.format_exc()
            }
            self.logger.error(f"❌ 方案2验证异常: {e}")
            self.overall_success = False
    
    async def _validate_solution3_websocket_pool_management(self):
        """🔥 验证方案3: WebSocket连接池管理缺陷修复"""
        try:
            self.logger.info("🔍 验证方案3: WebSocket连接池管理缺陷修复")
            
            # 检查统一WebSocket连接池管理器
            from websocket.unified_websocket_pool_manager import get_unified_websocket_pool_manager
            pool_manager = get_unified_websocket_pool_manager()
            
            # 验证核心功能
            features = []
            if hasattr(pool_manager, 'register_websocket_client'):
                features.append("客户端注册")
            if hasattr(pool_manager, 'handle_client_reconnect'):
                features.append("智能重连")
            if hasattr(pool_manager, 'get_pool_status'):
                features.append("状态监控")
            if hasattr(pool_manager, 'force_reconnect_all'):
                features.append("批量重连")
            
            # 测试状态获取
            try:
                status = await pool_manager.get_pool_status()
                if isinstance(status, dict) and "status" in status:
                    features.append("状态查询")
            except Exception:
                pass
            
            if len(features) >= 4:
                self.validation_results["solution_implementations"]["solution3"] = {
                    "status": "PASSED",
                    "message": "WebSocket连接池管理修复验证成功",
                    "details": f"实现功能: {', '.join(features)}"
                }
                self.logger.info("✅ 方案3验证通过")
            else:
                self.validation_results["solution_implementations"]["solution3"] = {
                    "status": "PARTIAL",
                    "message": "WebSocket连接池管理部分实施",
                    "details": f"实现功能: {', '.join(features)}"
                }
                self.logger.warning("⚠️ 方案3部分通过")
                
        except Exception as e:
            self.validation_results["solution_implementations"]["solution3"] = {
                "status": "FAILED",
                "message": f"方案3验证异常: {e}",
                "details": traceback.format_exc()
            }
            self.logger.error(f"❌ 方案3验证异常: {e}")
            self.overall_success = False
    
    async def _run_integration_tests(self):
        """🔥 运行集成测试"""
        self.logger.info("📋 阶段4: 集成测试")
        
        try:
            # 测试模块间协作
            integration_score = 0
            total_tests = 3
            
            # 测试1: 统一连接池与WebSocket客户端集成
            try:
                from websocket.unified_connection_pool_manager import get_connection_pool_manager
                from websocket.unified_websocket_pool_manager import get_unified_websocket_pool_manager
                
                connection_pool = get_connection_pool_manager()
                websocket_pool = get_unified_websocket_pool_manager()
                
                # 验证两个管理器可以协同工作
                if hasattr(websocket_pool, 'connection_pool_manager'):
                    integration_score += 1
                    self.logger.info("✅ 连接池集成测试通过")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ 连接池集成测试失败: {e}")
            
            # 测试2: API优化器与配置验证器集成
            try:
                from core.api_call_optimizer import get_api_call_optimizer
                from config.universal_config_validator import UniversalConfigValidator
                
                optimizer = get_api_call_optimizer()
                validator = UniversalConfigValidator()
                
                # 基本功能验证
                if optimizer and validator:
                    integration_score += 1
                    self.logger.info("✅ API优化器集成测试通过")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ API优化器集成测试失败: {e}")
            
            # 测试3: 时间戳处理器与错误处理器集成
            try:
                from websocket.unified_timestamp_processor import get_timestamp_processor
                
                processor = get_timestamp_processor("test")
                if processor:
                    integration_score += 1
                    self.logger.info("✅ 时间戳处理器集成测试通过")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ 时间戳处理器集成测试失败: {e}")
            
            # 生成集成测试结果
            integration_rate = integration_score / total_tests * 100
            
            if integration_rate >= 80:
                self.validation_results["integration_tests"]["overall"] = {
                    "status": "PASSED",
                    "message": f"集成测试通过率: {integration_rate:.1f}%",
                    "details": f"通过 {integration_score}/{total_tests} 项测试"
                }
                self.logger.info("✅ 集成测试整体通过")
            else:
                self.validation_results["integration_tests"]["overall"] = {
                    "status": "FAILED",
                    "message": f"集成测试通过率不足: {integration_rate:.1f}%",
                    "details": f"仅通过 {integration_score}/{total_tests} 项测试"
                }
                self.logger.warning("⚠️ 集成测试整体不通过")
                self.overall_success = False
                
        except Exception as e:
            self.validation_results["integration_tests"]["overall"] = {
                "status": "FAILED",
                "message": f"集成测试异常: {e}",
                "details": traceback.format_exc()
            }
            self.logger.error(f"❌ 集成测试异常: {e}")
            self.overall_success = False
    
    async def _run_performance_tests(self):
        """🔥 运行性能测试"""
        self.logger.info("📋 阶段5: 性能测试")
        
        try:
            performance_score = 0
            total_tests = 2
            
            # 性能测试1: 模块导入性能
            import_start = time.time()
            try:
                # 测试关键模块导入时间
                from core.api_call_optimizer import get_api_call_optimizer
                from websocket.unified_connection_pool_manager import get_connection_pool_manager
                from config.universal_config_validator import UniversalConfigValidator
                
                import_time = time.time() - import_start
                
                if import_time < 2.0:  # 2秒内导入完成
                    performance_score += 1
                    self.logger.info(f"✅ 模块导入性能测试通过: {import_time:.3f}秒")
                else:
                    self.logger.warning(f"⚠️ 模块导入性能较慢: {import_time:.3f}秒")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ 模块导入性能测试失败: {e}")
            
            # 性能测试2: 配置验证性能
            validation_start = time.time()
            try:
                from config.universal_config_validator import UniversalConfigValidator
                validator = UniversalConfigValidator()
                
                # 测试小批量验证性能
                test_symbols = ["BTC-USDT", "ETH-USDT"]
                await validator.validate_symbols_across_exchanges(test_symbols)
                
                validation_time = time.time() - validation_start
                
                if validation_time < 3.0:  # 3秒内完成小批量验证
                    performance_score += 1
                    self.logger.info(f"✅ 配置验证性能测试通过: {validation_time:.3f}秒")
                else:
                    self.logger.warning(f"⚠️ 配置验证性能较慢: {validation_time:.3f}秒")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ 配置验证性能测试失败: {e}")
            
            # 生成性能测试结果
            performance_rate = performance_score / total_tests * 100
            
            if performance_rate >= 80:
                self.validation_results["performance_tests"]["overall"] = {
                    "status": "PASSED",
                    "message": f"性能测试通过率: {performance_rate:.1f}%",
                    "details": f"通过 {performance_score}/{total_tests} 项测试"
                }
                self.logger.info("✅ 性能测试整体通过")
            else:
                self.validation_results["performance_tests"]["overall"] = {
                    "status": "WARNING",
                    "message": f"性能测试通过率: {performance_rate:.1f}%",
                    "details": f"通过 {performance_score}/{total_tests} 项测试，性能可能需要优化"
                }
                self.logger.warning("⚠️ 性能测试需要关注")
                
        except Exception as e:
            self.validation_results["performance_tests"]["overall"] = {
                "status": "FAILED",
                "message": f"性能测试异常: {e}",
                "details": traceback.format_exc()
            }
            self.logger.error(f"❌ 性能测试异常: {e}")
    
    def _generate_final_report(self, validation_time: float) -> Dict[str, Any]:
        """🔥 生成最终验证报告"""
        # 统计各类别通过情况
        category_stats = {}
        overall_passed = 0
        overall_total = 0
        
        for category, results in self.validation_results.items():
            passed = sum(1 for result in results.values() 
                        if isinstance(result, dict) and result.get("status") == "PASSED")
            total = len(results)
            
            category_stats[category] = {
                "passed": passed,
                "total": total,
                "rate": (passed / total * 100) if total > 0 else 0
            }
            
            overall_passed += passed
            overall_total += total
        
        overall_success_rate = (overall_passed / overall_total * 100) if overall_total > 0 else 0
        
        # 生成最终报告
        final_report = {
            "validation_time": validation_time,
            "overall_success": self.overall_success and overall_success_rate >= 80,
            "overall_success_rate": overall_success_rate,
            "category_stats": category_stats,
            "detailed_results": self.validation_results,
            "summary": {
                "total_tests": overall_total,
                "passed_tests": overall_passed,
                "failed_tests": overall_total - overall_passed
            }
        }
        
        return final_report
    
    def print_validation_report(self, report: Dict[str, Any]):
        """🔥 打印验证报告"""
        print("\n" + "="*80)
        print("🔥 综合验证报告 - 所有修复和方案实施验证")
        print("="*80)
        
        print(f"📊 验证摘要:")
        print(f"   验证耗时: {report['validation_time']:.2f}秒")
        print(f"   总体成功率: {report['overall_success_rate']:.1f}%")
        print(f"   验证结果: {'✅ 通过' if report['overall_success'] else '❌ 失败'}")
        
        print(f"\n📋 分类统计:")
        for category, stats in report['category_stats'].items():
            category_name = {
                "bug_fixes": "Bug修复",
                "problem_fixes": "问题修复", 
                "solution_implementations": "方案实施",
                "integration_tests": "集成测试",
                "performance_tests": "性能测试"
            }.get(category, category)
            
            print(f"   {category_name}: {stats['passed']}/{stats['total']} ({stats['rate']:.1f}%)")
        
        print(f"\n🏆 测试结果:")
        print(f"   总测试数: {report['summary']['total_tests']}")
        print(f"   通过测试: {report['summary']['passed_tests']}")
        print(f"   失败测试: {report['summary']['failed_tests']}")
        
        # 详细结果
        print(f"\n📝 详细结果:")
        for category, results in report['detailed_results'].items():
            category_name = {
                "bug_fixes": "Bug修复",
                "problem_fixes": "问题修复",
                "solution_implementations": "方案实施", 
                "integration_tests": "集成测试",
                "performance_tests": "性能测试"
            }.get(category, category)
            
            print(f"\n  {category_name}:")
            for test_name, result in results.items():
                if isinstance(result, dict):
                    status_icon = {
                        "PASSED": "✅",
                        "FAILED": "❌",
                        "PARTIAL": "⚠️",
                        "WARNING": "⚠️",
                        "SKIPPED": "⏭️"
                    }.get(result.get("status"), "❓")
                    
                    print(f"    {status_icon} {test_name}: {result.get('message', 'N/A')}")
        
        print("\n" + "="*80)


async def main():
    """主函数"""
    try:
        print("🔥 开始综合验证 - 验证所有修复和方案实施")
        print("="*60)
        
        validator = ComprehensiveValidator()
        report = await validator.run_comprehensive_validation()
        
        # 打印验证报告
        validator.print_validation_report(report)
        
        # 保存验证报告
        report_file = f"validation_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 验证报告已保存: {report_file}")
        
        # 返回退出码
        if report.get("overall_success", False):
            print("🎉 所有验证通过！系统修复和方案实施成功")
            return 0
        else:
            print("💥 验证失败！请检查失败的测试项目")
            return 1
            
    except Exception as e:
        print(f"❌ 综合验证运行异常: {e}")
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    
    # 运行综合验证
    result = asyncio.run(main())
    sys.exit(result)