# 🎯 **修复提示词和工作流程**

## 📋 **核心理念**

### **套利流程**
达到期货溢价（+）阈值开仓 → 锁定差价 → 等待趋同 → 现货溢价（-）达到阈值 → 平仓

**重要说明**:
- `+` 代表期货溢价
- `-` 代表现货溢价（不代表负数！）

### **核心原则**
**一切以通用系统支持任意代币的角度来深度审查修复！**
确保差价精准性、三交易所一致性（除非交易所特定规则需求）、高速性能的前提下进行！所有交易所使用相同的运行逻辑、重试和错误处理逻辑

---

### **任务目标** 
审查 logs 中的日志， 为什么 系统启动失败！
2 okx限速问题， 已经修复了很多次，依然无效！ 


## 🔍 **第一阶段：精准定位问题**

### **1. 文档研读**
1. 查看 `docs` 目录，尤其是 `07` 系列文档
2. 多看文档确保足够了解系统架构
3. 禁止造轮子，严格按照要求来实现

### **2. 代码审查**
1. 深度检查实际代码
2. 进行手动审查，识别潜在问题

### **3. 诊断脚本**
1. 手动审查完毕后，创建精确的诊断脚本
2. 精准定位错误，模拟失败场景
3. 确保诊断脚本的质量和修复方向正确

---

## 🧠 **第二阶段：深度思考问题**

### **📋 内部检查清单（每次检查完毕后必须回答）**

1. **现有架构中是否已有此功能？**（引用【系统核心知识库】中的模块列表回答）
2. **是否应该在统一模块中实现？**（统一模块）
3. **问题的根本原因是什么？**（基于【精准定位问题】的分析）
4. **检查链路和接口的结果是什么？**（基于【精准定位问题】的分析）
5. **其他两个交易所是否有同样问题？**（基于【精准定位问题】的分析）
6. **如何从源头最优解决问题？**（基于【统一修复】的原则）
7. **是否重复调用，存在造轮子？**（进行对比，优质整合删除）
8. **横向深度全面查阅资料并思考！**（包括 `docs` 中的md文档，和项目内官方SDK）

**永远不要忘记**：这是个通用多代币期货溢价套利系统！

---

## ⚙️ **第三阶段：优化规则和要求**

### **🔗 链路完整性与一致性优化**

1. **接口统一**: 所有接口参数、入参顺序、命名风格必须保持统一
2. **链路完整**: 严格避免链路中断（如：调用丢失、上下游类型不匹配、数据未透传）
3. **结构简化**: 自动合并冗余的调用路径或重复链路，提高结构简洁性与稳定性
4. **文档更新**:
   - 修复记录更新到 `123\docs\07B_核心问题修复专项文档.md`
   - 功能新增更新到 `123\docs\07_全流程工作流文档.md`
   - 保持07_全流程工作流的权威性和整洁
5. **手动修复**: 禁止使用修复脚本进行修复，必须手动修复！

### **✅ 质量保证检查**

**100%确定修复质量**：
- 修复优化没有造车轮？
- 使用了统一模块？
- 没有引入新的问题？
- 完美修复？
- 确保功能实现？
- 职责清晰，没有重复，没有冗余？
- 没有接口不统一、接口不兼容、链路错误？
- 测试非常权威没有问题？

### **🚫 禁止事项**
- 禁止任何模拟数据！
- 禁止造轮子！
- 禁止使用修复脚本！
  
