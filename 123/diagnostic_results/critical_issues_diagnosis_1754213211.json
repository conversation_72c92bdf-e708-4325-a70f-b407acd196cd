{"timestamp": "2025-08-03T11:26:51.541441", "diagnosis_summary": {"total_critical_bugs": 2, "total_system_issues": 3, "overall_status": "CRITICAL", "diagnosis_completion": "100%", "next_steps": "根据修复建议进行手动修复"}, "critical_bugs": {"bug5": {"status": "CONFIRMED", "severity": "CRITICAL", "file_path": "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/error_handler.py", "issues_found": [{"line_number": 289, "code": "total_attempts = sum(len([e for e in self.error_events if e.retry_count > 0]))", "issue": "TypeError: 'int' object is not iterable", "explanation": "len()返回整数，对整数使用sum()会导致TypeError"}, {"line_number": 289, "code": "total_attempts = sum(len([e for e in self.error_events if e.retry_count > 0]))", "issue": "TypeError: 'int' object is not iterable", "explanation": "len()返回整数，对整数使用sum()会导致TypeError"}, {"line_number": 290, "code": "successful_recoveries = sum(len([e for e in self.error_events if e.resolved]))", "issue": "TypeError: 'int' object is not iterable", "explanation": "len()返回整数，对整数使用sum()会导致TypeError"}, {"line_number": 290, "code": "successful_recoveries = sum(len([e for e in self.error_events if e.resolved]))", "issue": "TypeError: 'int' object is not iterable", "explanation": "len()返回整数，对整数使用sum()会导致TypeError"}], "fix_required": true, "recommended_fix": "移除sum()，直接使用len()计算列表长度"}, "bug6": {"status": "CONFIRMED", "severity": "CRITICAL", "risks_found": [{"file": "websocket/ws_client.py", "line_number": 249, "code": "await self.ws.close()", "risk_type": "DIRECT_CLOSE_WITHOUT_CHECK", "severity": "HIGH"}, {"file": "websocket/ws_client.py", "line_number": 249, "code": "await self.ws.close()", "risk_type": "POTENTIAL_NULL_ACCESS", "severity": "MEDIUM"}, {"file": "websocket/ws_client.py", "line_number": 326, "code": "await self.ws.close()", "risk_type": "DIRECT_CLOSE_WITHOUT_CHECK", "severity": "HIGH"}, {"file": "websocket/ws_client.py", "line_number": 326, "code": "await self.ws.close()", "risk_type": "POTENTIAL_NULL_ACCESS", "severity": "MEDIUM"}, {"file": "websocket/ws_client.py", "line_number": 330, "code": "await self.ws.close()", "risk_type": "DIRECT_CLOSE_WITHOUT_CHECK", "severity": "HIGH"}, {"file": "websocket/ws_client.py", "line_number": 330, "code": "await self.ws.close()", "risk_type": "POTENTIAL_NULL_ACCESS", "severity": "MEDIUM"}, {"file": "websocket/ws_client.py", "line_number": 395, "code": "await self.ws.close()", "risk_type": "DIRECT_CLOSE_WITHOUT_CHECK", "severity": "HIGH"}, {"file": "websocket/ws_client.py", "line_number": 395, "code": "await self.ws.close()", "risk_type": "POTENTIAL_NULL_ACCESS", "severity": "MEDIUM"}, {"file": "websocket/ws_client.py", "line_number": 651, "code": "message = await asyncio.wait_for(self.ws.recv(), timeout=timeout)", "risk_type": "POTENTIAL_NULL_ACCESS", "severity": "MEDIUM"}, {"file": "websocket/ws_client.py", "line_number": 707, "code": "await self.ws.close()", "risk_type": "DIRECT_CLOSE_WITHOUT_CHECK", "severity": "HIGH"}, {"file": "websocket/ws_client.py", "line_number": 707, "code": "await self.ws.close()", "risk_type": "POTENTIAL_NULL_ACCESS", "severity": "MEDIUM"}, {"file": "websocket/ws_client.py", "line_number": 736, "code": "await self.ws.close()", "risk_type": "DIRECT_CLOSE_WITHOUT_CHECK", "severity": "HIGH"}, {"file": "websocket/ws_client.py", "line_number": 736, "code": "await self.ws.close()", "risk_type": "POTENTIAL_NULL_ACCESS", "severity": "MEDIUM"}, {"file": "websocket/ws_client.py", "line_number": 775, "code": "await asyncio.wait_for(self.ws.ping(), timeout=5.0)", "risk_type": "POTENTIAL_NULL_ACCESS", "severity": "MEDIUM"}, {"file": "websocket/ws_client.py", "line_number": 876, "code": "await self.ws.close()", "risk_type": "DIRECT_CLOSE_WITHOUT_CHECK", "severity": "HIGH"}, {"file": "websocket/ws_client.py", "line_number": 876, "code": "await self.ws.close()", "risk_type": "POTENTIAL_NULL_ACCESS", "severity": "MEDIUM"}, {"file": "websocket/okx_ws.py", "line_number": 100, "code": "await self.ws.close()", "risk_type": "DIRECT_CLOSE_WITHOUT_CHECK", "severity": "HIGH"}, {"file": "websocket/okx_ws.py", "line_number": 100, "code": "await self.ws.close()", "risk_type": "POTENTIAL_NULL_ACCESS", "severity": "MEDIUM"}, {"file": "websocket/okx_ws.py", "line_number": 529, "code": "await self.ws.close()", "risk_type": "DIRECT_CLOSE_WITHOUT_CHECK", "severity": "HIGH"}, {"file": "websocket/okx_ws.py", "line_number": 529, "code": "await self.ws.close()", "risk_type": "POTENTIAL_NULL_ACCESS", "severity": "MEDIUM"}], "fix_required": true, "recommended_fix": "在调用ws方法前添加if self.ws is not None检查"}, "bug7": {"status": "REQUIRES_RUNTIME_CHECK", "severity": "HIGH", "file_path": "/root/myproject/123/69C 修复了一部分，的备份/123/websocket/unified_timestamp_processor.py", "sync_issues": [{"line_number": 284, "code": "sync_status=\"not_synced\",", "context": "同步状态设置"}], "note": "需要运行时检查同步状态逻辑"}}, "system_issues": {"api_config_inconsistency": {"status": "CONFIRMED", "severity": "HIGH", "configs_found": {"api_optimizer_okx": 3, "okx_exchange_rate_limit": 2}, "inconsistency": true, "details": {"api_optimizer": 3, "okx_exchange": 2}}, "contract_info_failures": {"status": "CONFIRMED", "severity": "HIGH", "failures_found": [{"exchange": "okx", "symbol": "ICNT-USDT", "log_file": "error_20250803.log"}, {"exchange": "okx", "symbol": "CAKE-USDT", "log_file": "error_20250803.log"}], "total_failures": 2, "affected_pairs": ["okx_ICNT-USDT", "okx_CAKE-USDT"]}, "trading_pair_validation": {"status": "SYMBOLS_FOUND", "severity": "MEDIUM", "target_symbols": ["SPK-USDT", "RESOLV-USDT", "ICNT-USDT", "CAKE-USDT", "WIF-USDT", "AI16Z-USDT", "SOL-USDT", "MATIC-USDT", "DOT-USDT", "JUP-USDT"], "total_symbols": 10, "validation_needed": true, "note": "需要验证这些交易对在各交易所的支持情况"}, "websocket_connection": {"status": "ISSUES_FOUND", "severity": "HIGH", "issues_summary": {"http_503_errors": ["websocket_error_recovery_20250803.log", "error_20250803.log"], "too_many_requests": ["OKXExchange.log", "error_20250803.log"], "null_pointer_errors": ["websocket_error_recovery_20250803.log", "error_20250803.log"]}, "total_issue_files": 6}}, "config_inconsistencies": {}, "recommendations": ["🔥 紧急: 修复错误处理器统计计算逻辑，移除错误的sum(len())调用", "🔥 紧急: 添加WebSocket连接空指针检查，防止NoneType错误", "⚠️ 高优先级: 统一API限速配置，确保调用优化器和交易所设置一致", "⚠️ 高优先级: 修复合约信息获取失败问题，实施重试机制", "⚠️ 高优先级: 解决WebSocket连接问题，优化API限流机制"]}