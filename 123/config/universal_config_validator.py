#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 通用配置验证器 - 解决问题9：交易对配置验证缺失

设计原则：
1. 通用系统：不针对任何特定代币，适用于所有交易对
2. 三交易所一致性：确保所有交易所使用统一的验证逻辑
3. 动态验证：启动时检查配置的交易对在各交易所的实际支持度
4. 智能建议：提供修复建议，协助优化配置
"""

import asyncio
import logging
from typing import Dict, List, Set, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class SupportLevel(Enum):
    """支持度级别"""
    FULLY_SUPPORTED = "完全支持"      # 现货+期货都支持
    SPOT_ONLY = "仅现货支持"         # 只支持现货
    FUTURES_ONLY = "仅期货支持"      # 只支持期货
    NOT_SUPPORTED = "不支持"         # 完全不支持
    UNKNOWN = "未知"                # 无法确定


@dataclass
class SymbolValidationResult:
    """交易对验证结果"""
    symbol: str
    exchange: str
    support_level: SupportLevel
    spot_available: bool
    futures_available: bool
    error_message: Optional[str] = None
    api_response: Optional[Dict] = None


class MockExchange:
    """🔥 模拟交易所（用于测试）"""
    
    def __init__(self, name: str):
        self.name = name
        
        # 模拟支持的交易对
        self.supported_symbols = {
            "gate": ["BTC-USDT", "ETH-USDT", "DOGE-USDT"],
            "okx": ["BTC-USDT", "ETH-USDT", "DOGE-USDT", "ADA-USDT"],
            "bybit": ["BTC-USDT", "ETH-USDT", "SOL-USDT"]
        }.get(name, ["BTC-USDT", "ETH-USDT"])
    
    async def validate_symbol_support(self, symbol: str) -> SymbolValidationResult:
        """模拟验证交易对支持度"""
        is_supported = symbol in self.supported_symbols
        
        return SymbolValidationResult(
            symbol=symbol,
            exchange=self.name,
            support_level=SupportLevel.FULLY_SUPPORTED if is_supported else SupportLevel.NOT_SUPPORTED,
            spot_available=is_supported,
            futures_available=is_supported
        )


@dataclass
class ConfigValidationReport:
    """配置验证报告"""
    total_symbols: int
    validated_symbols: int
    fully_supported_symbols: List[str]
    problematic_symbols: Dict[str, Dict[str, str]]  # symbol -> {exchange: reason}
    recommendations: List[str]
    summary: str


class UniversalConfigValidator:
    """
    🔥 通用配置验证器
    
    提供通用的交易对配置验证功能，确保所有配置的交易对在目标交易所中得到支持
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.supported_exchanges = ["gate", "bybit", "okx"]
        self.validation_cache = {}  # 缓存验证结果
        
    async def validate_symbol_support(self, symbol: str, exchange_client, exchange_name: str) -> SymbolValidationResult:
        """
        🔥 验证单个交易对在指定交易所的支持度
        
        Args:
            symbol: 交易对符号（标准格式，如 BTC-USDT）
            exchange_client: 交易所客户端实例
            exchange_name: 交易所名称
            
        Returns:
            SymbolValidationResult: 验证结果
        """
        try:
            # 🔥 使用缓存避免重复验证
            cache_key = f"{exchange_name}_{symbol}"
            if cache_key in self.validation_cache:
                return self.validation_cache[cache_key]
            
            # 🔥 转换为交易所特定格式
            exchange_symbol = self._convert_symbol_format(symbol, exchange_name)
            
            spot_available = False
            futures_available = False
            error_message = None
            
            # 🔥 检查现货支持度
            try:
                if hasattr(exchange_client, 'get_spot_symbols'):
                    spot_symbols = await exchange_client.get_spot_symbols()
                    spot_available = exchange_symbol in spot_symbols
                elif hasattr(exchange_client, 'get_ticker'):
                    # 回退方案：尝试获取ticker
                    ticker = await exchange_client.get_ticker(exchange_symbol)
                    spot_available = ticker is not None
            except Exception as spot_error:
                self.logger.debug(f"{exchange_name} {symbol} 现货检查失败: {spot_error}")
            
            # 🔥 检查期货支持度
            try:
                if hasattr(exchange_client, 'get_futures_symbols'):
                    futures_symbols = await exchange_client.get_futures_symbols()
                    futures_available = exchange_symbol in futures_symbols
                elif hasattr(exchange_client, 'get_contract_info'):
                    # 回退方案：尝试获取合约信息
                    contract_info = await exchange_client.get_contract_info(exchange_symbol)
                    futures_available = contract_info is not None
            except Exception as futures_error:
                self.logger.debug(f"{exchange_name} {symbol} 期货检查失败: {futures_error}")
            
            # 🔥 确定支持级别
            if spot_available and futures_available:
                support_level = SupportLevel.FULLY_SUPPORTED
            elif spot_available:
                support_level = SupportLevel.SPOT_ONLY
            elif futures_available:
                support_level = SupportLevel.FUTURES_ONLY
            else:
                support_level = SupportLevel.NOT_SUPPORTED
                error_message = f"在{exchange_name}中未找到{symbol}的现货或期货支持"
            
            result = SymbolValidationResult(
                symbol=symbol,
                exchange=exchange_name,
                support_level=support_level,
                spot_available=spot_available,
                futures_available=futures_available,
                error_message=error_message
            )
            
            # 🔥 缓存结果
            self.validation_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"验证 {exchange_name} {symbol} 支持度时异常: {e}")
            return SymbolValidationResult(
                symbol=symbol,
                exchange=exchange_name,
                support_level=SupportLevel.UNKNOWN,
                spot_available=False,
                futures_available=False,
                error_message=str(e)
            )
    
    def _convert_symbol_format(self, symbol: str, exchange_name: str) -> str:
        """
        🔥 将标准格式交易对转换为交易所特定格式
        
        Args:
            symbol: 标准格式（BTC-USDT）
            exchange_name: 交易所名称
            
        Returns:
            str: 交易所特定格式的交易对
        """
        try:
            exchange_lower = exchange_name.lower()
            
            if exchange_lower == "gate":
                # Gate.io: BTC-USDT -> BTC_USDT
                return symbol.replace('-', '_')
            elif exchange_lower == "bybit":
                # Bybit: BTC-USDT -> BTCUSDT
                return symbol.replace('-', '')
            elif exchange_lower == "okx":
                # OKX: 保持 BTC-USDT
                return symbol
            else:
                # 其他交易所：保持原格式
                return symbol
                
        except Exception as e:
            self.logger.warning(f"格式转换失败 {symbol} -> {exchange_name}: {e}")
            return symbol
    
    async def validate_all_configured_symbols(self, exchange_clients: Dict[str, Any], configured_symbols: List[str]) -> ConfigValidationReport:
        """
        🔥 验证所有配置的交易对在各交易所的支持度
        
        Args:
            exchange_clients: 交易所客户端字典 {exchange_name: client}
            configured_symbols: 配置的交易对列表
            
        Returns:
            ConfigValidationReport: 完整的验证报告
        """
        try:
            self.logger.info(f"🔍 开始验证 {len(configured_symbols)} 个配置的交易对...")
            
            validation_results = {}  # symbol -> {exchange: result}
            problematic_symbols = {}
            fully_supported_symbols = []
            recommendations = []
            
            # 🔥 并发验证所有交易对在所有交易所的支持度
            for symbol in configured_symbols:
                validation_results[symbol] = {}
                symbol_problems = {}
                
                for exchange_name, exchange_client in exchange_clients.items():
                    if exchange_name.lower() in self.supported_exchanges:
                        result = await self.validate_symbol_support(symbol, exchange_client, exchange_name)
                        validation_results[symbol][exchange_name] = result
                        
                        # 🔥 检查是否存在问题
                        if result.support_level == SupportLevel.NOT_SUPPORTED:
                            symbol_problems[exchange_name] = f"不支持 {symbol}"
                        elif result.support_level == SupportLevel.SPOT_ONLY:
                            symbol_problems[exchange_name] = f"仅现货支持，缺少期货支持"
                        elif result.support_level == SupportLevel.FUTURES_ONLY:
                            symbol_problems[exchange_name] = f"仅期货支持，缺少现货支持"
                        elif result.support_level == SupportLevel.UNKNOWN:
                            symbol_problems[exchange_name] = f"无法确定支持状态: {result.error_message}"
                
                # 🔥 分析该交易对的整体支持情况
                if not symbol_problems:
                    fully_supported_symbols.append(symbol)
                else:
                    problematic_symbols[symbol] = symbol_problems
            
            # 🔥 生成通用建议（不针对特定代币）
            recommendations = self._generate_universal_recommendations(validation_results, problematic_symbols)
            
            # 🔥 生成验证报告
            report = ConfigValidationReport(
                total_symbols=len(configured_symbols),
                validated_symbols=len(validation_results),
                fully_supported_symbols=fully_supported_symbols,
                problematic_symbols=problematic_symbols,
                recommendations=recommendations,
                summary=self._generate_summary(len(configured_symbols), len(fully_supported_symbols), len(problematic_symbols))
            )
            
            self.logger.info(f"✅ 配置验证完成: {report.summary}")
            return report
            
        except Exception as e:
            self.logger.error(f"配置验证过程异常: {e}")
            return ConfigValidationReport(
                total_symbols=len(configured_symbols),
                validated_symbols=0,
                fully_supported_symbols=[],
                problematic_symbols={},
                recommendations=[f"验证过程异常: {e}"],
                summary="验证失败"
            )
    
    def _generate_universal_recommendations(self, validation_results: Dict, problematic_symbols: Dict) -> List[str]:
        """
        🔥 生成通用建议（不针对特定代币）
        """
        recommendations = []
        
        # 🔥 统计各交易所的支持度问题
        exchange_issues = {"gate": 0, "bybit": 0, "okx": 0}
        support_type_issues = {"spot_only": 0, "futures_only": 0, "not_supported": 0}
        
        for symbol, problems in problematic_symbols.items():
            for exchange, reason in problems.items():
                exchange_issues[exchange] += 1
                if "仅现货支持" in reason:
                    support_type_issues["spot_only"] += 1
                elif "仅期货支持" in reason:
                    support_type_issues["futures_only"] += 1
                elif "不支持" in reason:
                    support_type_issues["not_supported"] += 1
        
        # 🔥 生成通用建议
        if support_type_issues["not_supported"] > 0:
            recommendations.append(f"发现 {support_type_issues['not_supported']} 个完全不支持的交易对，建议从配置中移除")
        
        if support_type_issues["spot_only"] > 0:
            recommendations.append(f"发现 {support_type_issues['spot_only']} 个仅现货支持的交易对，无法进行期现套利")
        
        if support_type_issues["futures_only"] > 0:
            recommendations.append(f"发现 {support_type_issues['futures_only']} 个仅期货支持的交易对，无法进行期现套利")
        
        # 🔥 交易所特定建议
        for exchange, issue_count in exchange_issues.items():
            if issue_count > 0:
                recommendations.append(f"{exchange.upper()} 交易所有 {issue_count} 个交易对存在支持度问题，建议检查API文档")
        
        # 🔥 通用优化建议
        if problematic_symbols:
            recommendations.append("建议优先配置在所有交易所都完全支持的交易对，以确保套利系统正常运行")
            recommendations.append("可以通过实时API查询各交易所支持的交易对列表，动态更新配置")
        
        return recommendations
    
    def _generate_summary(self, total: int, supported: int, problematic: int) -> str:
        """生成验证摘要"""
        success_rate = (supported / total * 100) if total > 0 else 0
        return f"验证完成：{total} 个交易对中 {supported} 个完全支持，{problematic} 个存在问题，成功率 {success_rate:.1f}%"
    
    def print_validation_report(self, report: ConfigValidationReport):
        """
        🔥 打印详细的验证报告
        """
        print("\n" + "="*60)
        print("🔍 通用配置验证报告")
        print("="*60)
        
        print(f"📊 验证摘要: {report.summary}")
        print(f"✅ 完全支持的交易对 ({len(report.fully_supported_symbols)}):")
        for symbol in report.fully_supported_symbols:
            print(f"   - {symbol}")
        
        if report.problematic_symbols:
            print(f"\n⚠️ 存在问题的交易对 ({len(report.problematic_symbols)}):")
            for symbol, problems in report.problematic_symbols.items():
                print(f"   - {symbol}:")
                for exchange, reason in problems.items():
                    print(f"     └─ {exchange.upper()}: {reason}")
        
        if report.recommendations:
            print(f"\n💡 优化建议:")
            for i, rec in enumerate(report.recommendations, 1):
                print(f"   {i}. {rec}")
        
        print("\n" + "="*60)
    
    async def validate_symbols_across_exchanges(self, symbols: List[str], exchange_clients: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """🔥 新增：验证交易对在所有交易所的支持度（别名方法）"""
        try:
            if not exchange_clients:
                # 创建模拟的交易所客户端用于测试
                exchange_clients = {
                    "gate": MockExchange("gate"),
                    "okx": MockExchange("okx"),
                    "bybit": MockExchange("bybit")
                }
            
            report = await self.validate_all_configured_symbols(exchange_clients, symbols)
            
            # 转换为验证脚本期望的格式
            validation_results = []
            for symbol in symbols:
                is_valid = symbol in report.fully_supported_symbols
                validation_results.append({
                    "symbol": symbol,
                    "is_valid": is_valid,
                    "support_level": "完全支持" if is_valid else "部分支持或不支持"
                })
            
            return {
                "validation_results": validation_results,
                "summary": report.summary,
                "total_symbols": report.total_symbols,
                "validated_symbols": report.validated_symbols
            }
            
        except Exception as e:
            self.logger.error(f"跨交易所验证失败: {e}")
            return {
                "validation_results": [],
                "summary": f"验证失败: {e}",
                "total_symbols": len(symbols),
                "validated_symbols": 0
            }

    async def generate_optimized_config_suggestion(self, report: ConfigValidationReport) -> str:
        """
        🔥 生成优化的配置建议
        """
        try:
            if not report.fully_supported_symbols:
                return "TARGET_SYMBOLS=  # 警告：没有找到完全支持的交易对"
            
            # 🔥 按字母顺序排序，确保配置整洁
            optimized_symbols = sorted(report.fully_supported_symbols)
            
            config_line = f"TARGET_SYMBOLS={','.join(optimized_symbols)}"
            
            comment = f"  # 优化后：{len(optimized_symbols)}个完全支持的交易对"
            
            return config_line + comment
            
        except Exception as e:
            self.logger.error(f"生成优化配置建议失败: {e}")
            return "# 配置优化建议生成失败，请手动检查交易对支持度"


# 🔥 全局实例
_global_validator = None


def get_universal_config_validator() -> UniversalConfigValidator:
    """获取全局配置验证器实例"""
    global _global_validator
    if _global_validator is None:
        _global_validator = UniversalConfigValidator()
    return _global_validator


async def validate_current_config(exchange_clients: Optional[Dict[str, Any]] = None) -> ConfigValidationReport:
    """
    🔥 验证当前系统配置
    
    Args:
        exchange_clients: 交易所客户端字典，如果不提供将尝试创建
        
    Returns:
        ConfigValidationReport: 验证报告
    """
    try:
        validator = get_universal_config_validator()
        
        # 🔥 获取当前配置的交易对
        from config.settings import ArbitrageConfig
        configured_symbols = ArbitrageConfig.get_target_symbols()
        
        if not exchange_clients:
            # 🔥 如果没有提供客户端，返回基础检查结果
            logger.warning("未提供交易所客户端，无法进行完整验证")
            return ConfigValidationReport(
                total_symbols=len(configured_symbols),
                validated_symbols=0,
                fully_supported_symbols=[],
                problematic_symbols={},
                recommendations=["需要提供交易所客户端以进行完整验证"],
                summary=f"配置了 {len(configured_symbols)} 个交易对，需要完整验证"
            )
        
        # 🔥 执行完整验证
        return await validator.validate_all_configured_symbols(exchange_clients, configured_symbols)
        
    except Exception as e:
        logger.error(f"验证当前配置时异常: {e}")
        return ConfigValidationReport(
            total_symbols=0,
            validated_symbols=0,
            fully_supported_symbols=[],
            problematic_symbols={},
            recommendations=[f"验证异常: {e}"],
            summary="验证失败"
        )