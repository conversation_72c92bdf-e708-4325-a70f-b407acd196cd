#!/usr/bin/env python3
"""
API调用优化器 - 🔥 方案1：OKX API限速智能优化系统

增强功能：
1. API调用缓存与去重机制
2. WebSocket优先级保护机制  
3. 精确API限速控制
"""

import asyncio
import time
import logging
from typing import Dict, List, Any, Optional, Tuple, Callable
from collections import defaultdict
import hashlib
import json

logger = logging.getLogger("api_optimizer")


class APICallCache:
    """🔥 新增：API调用缓存与去重机制"""
    
    def __init__(self):
        self.cache = {}  # cache_key -> (result, timestamp, ttl)
        self.default_ttl = 300  # 默认5分钟TTL
        
        # 🔥 不同API类型的缓存策略
        self.cache_policies = {
            "account_config": 1800,    # 账户配置缓存30分钟
            "contract_info": 300,      # 合约信息缓存5分钟
            "trading_rule": 600,       # 交易规则缓存10分钟
            "exchange_info": 1800,     # 交易所信息缓存30分钟
            "balance": 30,             # 余额缓存30秒
            "ticker": 10,              # 行情缓存10秒
        }
    
    def _generate_cache_key(self, exchange_name: str, method_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        # 创建参数的哈希值
        params_str = json.dumps({
            "args": args,
            "kwargs": {k: v for k, v in kwargs.items() if k != "self"}
        }, sort_keys=True)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        
        return f"{exchange_name}_{method_name}_{params_hash}"
    
    def _get_cache_policy(self, method_name: str) -> int:
        """根据方法名获取缓存策略"""
        for key, ttl in self.cache_policies.items():
            if key in method_name.lower():
                return ttl
        return self.default_ttl
    
    def get(self, cache_key: str) -> Optional[Any]:
        """获取缓存结果"""
        if cache_key in self.cache:
            result, timestamp, ttl = self.cache[cache_key]
            if time.time() - timestamp < ttl:
                return result
            else:
                # 缓存过期，删除
                del self.cache[cache_key]
        return None
    
    def set(self, cache_key: str, result: Any, ttl: Optional[int] = None) -> None:
        """设置缓存结果"""
        if ttl is None:
            ttl = self.default_ttl
        self.cache[cache_key] = (result, time.time(), ttl)
    
    def clear_expired(self) -> int:
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []
        
        for cache_key, (result, timestamp, ttl) in self.cache.items():
            if current_time - timestamp >= ttl:
                expired_keys.append(cache_key)
        
        for key in expired_keys:
            del self.cache[key]
        
        return len(expired_keys)


class WebSocketPriorityManager:
    """🔥 新增：WebSocket优先级保护机制"""
    
    def __init__(self):
        self.websocket_reserved_quota = {
            "gate": 2,    # 🔥 统一为2次/秒，确保三交易所一致性
            "bybit": 2,   # 🔥 统一为2次/秒，确保三交易所一致性
            "okx": 2      # 🔥 统一为2次/秒，确保三交易所一致性
        }
        
        self.priority_levels = {
            "websocket": 1,     # 最高优先级
            "critical": 2,      # 关键API
            "normal": 3,        # 普通API
            "batch": 4          # 批量处理
        }
        
        self.call_queues_by_priority = {
            exchange: {level: asyncio.Queue() for level in self.priority_levels.values()}
            for exchange in ["gate", "bybit", "okx"]
        }
    
    def get_priority_level(self, method_name: str, is_websocket_related: bool = False) -> int:
        """确定API调用的优先级"""
        if is_websocket_related:
            return self.priority_levels["websocket"]
        elif any(keyword in method_name.lower() for keyword in ["balance", "position", "order"]):
            return self.priority_levels["critical"]
        else:
            return self.priority_levels["normal"]
    
    async def enqueue_call(self, exchange_name: str, priority: int, call_info: tuple) -> None:
        """将调用加入优先级队列"""
        if exchange_name in self.call_queues_by_priority:
            await self.call_queues_by_priority[exchange_name][priority].put(call_info)
    
    async def dequeue_next_call(self, exchange_name: str) -> Optional[tuple]:
        """按优先级获取下一个调用"""
        if exchange_name not in self.call_queues_by_priority:
            return None
        
        # 按优先级顺序检查队列
        for priority in sorted(self.priority_levels.values()):
            queue = self.call_queues_by_priority[exchange_name][priority]
            if not queue.empty():
                return await queue.get()
        
        return None


class APICallOptimizer:
    """API调用优化器 - 🔥 增强版"""
    
    def __init__(self):
        self.logger = logger
        
        # 🔥 统一配置：三交易所一致性API限速
        self.rate_limits = {
            "gate": 2,     # 统一为2次/秒，确保三交易所一致性
            "bybit": 2,    # 统一为2次/秒，确保三交易所一致性
            "okx": 2       # 统一为2次/秒，确保三交易所一致性
        }

        # 🔥 新增：冷却时间配置
        self.cooldown_config = {
            "base_cooldown": 1.5,      
            "buffer_cooldown": 3.5,    
            "total_cooldown": 5.0      
        }
        
        # 🔥 新增：缓存与去重机制
        self.api_cache = APICallCache()
        
        # 🔥 新增：WebSocket优先级管理
        self.priority_manager = WebSocketPriorityManager()
        
        # 传统调用队列（保持向后兼容）
        self.call_queues = {
            "gate": asyncio.Queue(),
            "bybit": asyncio.Queue(),
            "okx": asyncio.Queue()
        }
        
        # 调用统计
        self.call_stats = defaultdict(int)
        self.cache_stats = defaultdict(int)
        
        # 🔥 新增：WebSocket保护状态
        self.websocket_protection_active = {}
        
    async def cached_api_call(self, exchange_name: str, func: Callable, *args, 
                            priority: str = "normal", is_websocket_related: bool = False, 
                            cache_enabled: bool = True, **kwargs) -> Any:
        """
        🔥 增强的API调用：集成缓存、去重、优先级管理
        
        Args:
            exchange_name: 交易所名称
            func: API函数
            priority: 优先级 ("websocket", "critical", "normal", "batch")
            is_websocket_related: 是否与WebSocket相关
            cache_enabled: 是否启用缓存
        """
        method_name = getattr(func, '__name__', str(func))
        
        # 🔥 检查缓存
        if cache_enabled:
            cache_key = self.api_cache._generate_cache_key(exchange_name, method_name, args, kwargs)
            cached_result = self.api_cache.get(cache_key)
            if cached_result is not None:
                self.cache_stats[f"{exchange_name}_hit"] += 1
                self.logger.debug(f"✅ [{exchange_name}] 缓存命中: {method_name}")
                return cached_result
            
            self.cache_stats[f"{exchange_name}_miss"] += 1
        
        # 🔥 WebSocket优先级保护
        if is_websocket_related:
            await self._ensure_websocket_quota(exchange_name)
        
        # 🔥 使用优先级队列
        priority_level = self.priority_manager.get_priority_level(method_name, is_websocket_related)
        
        # 执行限速调用
        result = await self._execute_priority_call(
            exchange_name, func, args, kwargs, priority_level
        )
        
        # 🔥 缓存结果
        if cache_enabled and result is not None:
            ttl = self.api_cache._get_cache_policy(method_name)
            cache_key = self.api_cache._generate_cache_key(exchange_name, method_name, args, kwargs)
            self.api_cache.set(cache_key, result, ttl)
            self.logger.debug(f"💾 [{exchange_name}] 缓存保存: {method_name} (TTL: {ttl}s)")
        
        return result
    
    async def _ensure_websocket_quota(self, exchange_name: str) -> None:
        """🔥 确保WebSocket调用有足够的API配额"""
        reserved_quota = self.priority_manager.websocket_reserved_quota.get(exchange_name, 1)
        
        # 检查当前API使用情况
        current_time = time.time()
        recent_calls = self.call_stats.get(f"{exchange_name}_recent", 0)
        
        # 如果接近限制，等待
        rate_limit = self.rate_limits[exchange_name]
        if recent_calls >= (rate_limit - reserved_quota):
            wait_time = 1.0  # 等待1秒让配额恢复
            self.logger.info(f"🔒 [{exchange_name}] WebSocket保护：等待API配额恢复 ({wait_time}s)")
            await asyncio.sleep(wait_time)
    
    async def _execute_priority_call(self, exchange_name: str, func: Callable, 
                                   args: tuple, kwargs: dict, priority: int) -> Any:
        """🔥 执行优先级API调用"""
        # 加入优先级队列
        call_info = (func, args, kwargs)
        await self.priority_manager.enqueue_call(exchange_name, priority, call_info)
        
        # 等待并执行
        return await self._process_priority_queue(exchange_name)
    
    async def _process_priority_queue(self, exchange_name: str) -> Any:
        """🔥 处理优先级队列"""
        # 获取下一个调用
        call_info = await self.priority_manager.dequeue_next_call(exchange_name)
        if not call_info:
            return None
        
        func, args, kwargs = call_info
        
        # 执行限速控制
        await self._rate_limit_wait(exchange_name)
        
        # 执行调用
        try:
            call_start_time = time.time()
            result = await func(*args, **kwargs)
            call_duration = time.time() - call_start_time
            
            # 更新统计
            self.call_stats[exchange_name] += 1
            self.call_stats[f"{exchange_name}_recent"] = self.call_stats.get(f"{exchange_name}_recent", 0) + 1
            
            self.logger.debug(f"✅ [{exchange_name}] API调用成功，耗时: {call_duration:.3f}s")
            return result
            
        except Exception as e:
            error_msg = str(e)
            
            # 🔥 智能错误处理
            if any(keyword in error_msg for keyword in ["Too Many Requests", "50011", "10006"]):
                await self._handle_rate_limit_error(exchange_name, func, args, kwargs)
            else:
                self.logger.error(f"❌ [{exchange_name}] API调用失败: {e}")
            
            return None
    
    async def _handle_rate_limit_error(self, exchange_name: str, func: Callable, 
                                     args: tuple, kwargs: dict) -> Any:
        """🔥 处理限速错误"""
        self.logger.warning(f"⚠️ [{exchange_name}] 触发限速错误，启动智能恢复策略")
        
        # 激活WebSocket保护
        self.websocket_protection_active[exchange_name] = True
        
        # 等待恢复
        await asyncio.sleep(5.0)
        
        # 指数退避重试
        for retry_count in range(3):
            retry_delay = 2 ** retry_count
            self.logger.info(f"🔄 [{exchange_name}] 限速恢复重试 {retry_count + 1}/3，延迟{retry_delay}s")
            await asyncio.sleep(retry_delay)
            
            try:
                result = await func(*args, **kwargs)
                self.logger.info(f"✅ [{exchange_name}] 限速恢复成功")
                self.websocket_protection_active[exchange_name] = False
                return result
            except Exception as retry_e:
                if retry_count == 2:
                    self.logger.error(f"❌ [{exchange_name}] 限速恢复失败: {retry_e}")
                continue
        
        self.websocket_protection_active[exchange_name] = False
        return None
        
    async def optimize_startup_api_calls(self, exchanges: Dict[str, Any], symbols: List[str]):
        """🔥 根源修复：分批预加载，确保30+代币健壮启动"""
        self.logger.info("🚀 开始分批预加载API调用优化...")

        # 🔥 实施分批预加载：每批5个任务，批间冷却2秒
        batch_size = 5
        batch_cooldown = 2.0

        # 1. 分批并行化交易规则预加载
        await self.batched_trading_rules_preload(exchanges, symbols, batch_size, batch_cooldown)

        # 2. 批量化余额查询（低频率）
        await self.batch_balance_queries(exchanges)

        # 3. 分批智能化合约信息获取
        await self.batched_contract_info_fetch(exchanges, symbols, batch_size, batch_cooldown)

        # 4. 延迟杠杆设置（最后执行）
        await self.delayed_leverage_setup(exchanges, symbols)

        self.logger.info("✅ 分批预加载API调用优化完成")
        
    async def parallel_trading_rules_preload(self, exchanges: Dict[str, Any], symbols: List[str]):
        """并行化交易规则预加载"""
        self.logger.info("📋 并行化交易规则预加载...")
        
        tasks = []
        
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                for market_type in ["spot", "futures"]:
                    task = self.rate_limited_api_call(
                        exchange_name,
                        self._get_trading_rule,
                        exchange, symbol, market_type
                    )
                    tasks.append(task)
        
        # 并行执行，但受限于各交易所的API限制
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        self.logger.info(f"✅ 交易规则预加载完成: {success_count}/{len(tasks)}")

    async def test_rate_limiting_effectiveness(self, exchange_name: str = "gate", test_calls: int = 5) -> Dict[str, Any]:
        """🔥 新增：测试限速效果的方法"""
        self.logger.info(f"🧪 测试{exchange_name}限速效果，调用次数: {test_calls}")

        rate_limit = self.rate_limits[exchange_name]
        expected_min_duration = (test_calls - 1) / rate_limit  # 前n-1次调用的最小间隔时间

        # 模拟API调用函数
        async def mock_api_call():
            await asyncio.sleep(0.01)  # 模拟10ms的API响应时间
            return {"status": "success", "timestamp": time.time()}

        # 执行测试
        test_start = time.time()
        results = []

        for i in range(test_calls):
            call_start = time.time()
            result = await self.rate_limited_api_call(exchange_name, mock_api_call)
            call_end = time.time()

            results.append({
                "call_index": i,
                "call_duration": call_end - call_start,
                "timestamp": call_end,
                "result": result is not None
            })

        test_duration = time.time() - test_start

        # 分析结果
        analysis = {
            "exchange": exchange_name,
            "rate_limit_per_sec": rate_limit,
            "test_calls": test_calls,
            "total_duration": test_duration,
            "expected_min_duration": expected_min_duration,
            "rate_limiting_working": test_duration >= expected_min_duration * 0.9,  # 允许10%误差
            "average_call_interval": test_duration / max(test_calls - 1, 1),
            "expected_interval": 1.0 / rate_limit,
            "results": results
        }

        self.logger.info(f"📊 {exchange_name}限速测试结果:")
        self.logger.info(f"   总耗时: {test_duration:.3f}秒 (预期最小: {expected_min_duration:.3f}秒)")
        self.logger.info(f"   平均间隔: {analysis['average_call_interval']:.3f}秒 (预期: {analysis['expected_interval']:.3f}秒)")
        self.logger.info(f"   限速有效: {'✅' if analysis['rate_limiting_working'] else '❌'}")

        return analysis

    async def _rate_limit_wait(self, exchange_name: str):
        """🔥 根源修复：健壮的限速等待方法，确保30+代币启动成功"""
        rate_limit = self.rate_limits.get(exchange_name, 10)

        # 🔥 根源修复：使用更保守的限速控制
        current_time = time.time()
        last_call_time = getattr(self, f"_{exchange_name}_last_call", 0)
        time_since_last = current_time - last_call_time

        # 🔥 优化修复：合理的冷却时间，平衡限速和性能
        if exchange_name == "okx":
            # OKX使用优化的冷却策略 - 调整到0.33秒支持3次/秒
            min_interval = 0.33  # 每秒3次调用，平衡限速和WebSocket性能
        else:
            # 其他交易所使用基础冷却 + 缓冲
            base_interval = 1.0 / rate_limit
            min_interval = max(base_interval, self.cooldown_config["base_cooldown"])

        # 🔥 确保严格遵守冷却时间
        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            self.logger.info(f"🕐 {exchange_name} 健壮冷却等待: {wait_time:.3f}秒 (要求: {min_interval:.3f}秒)")
            await asyncio.sleep(wait_time)

        # 🔥 修复：在等待完成后立即更新时间戳
        setattr(self, f"_{exchange_name}_last_call", time.time())

    async def batched_trading_rules_preload(self, exchanges: Dict[str, Any], symbols: List[str],
                                          batch_size: int, batch_cooldown: float):
        """🔥 新增：分批预加载交易规则，避免连续大量API调用"""
        self.logger.info(f"📦 开始分批预加载交易规则: 每批{batch_size}个，批间冷却{batch_cooldown}秒")

        # 创建所有任务
        all_tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                for market_type in ["spot", "futures"]:
                    task_info = {
                        "exchange_name": exchange_name,
                        "exchange": exchange,
                        "symbol": symbol,
                        "market_type": market_type
                    }
                    all_tasks.append(task_info)

        # 分批处理
        total_batches = (len(all_tasks) + batch_size - 1) // batch_size
        self.logger.info(f"📊 总任务数: {len(all_tasks)}, 分为{total_batches}批")

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(all_tasks))
            batch_tasks = all_tasks[start_idx:end_idx]

            self.logger.info(f"🔄 处理第{batch_idx + 1}/{total_batches}批 ({len(batch_tasks)}个任务)")

            # 并行执行当前批次
            batch_coroutines = []
            for task_info in batch_tasks:
                coro = self.rate_limited_api_call(
                    task_info["exchange_name"],
                    self._get_trading_rule,
                    task_info["exchange"], task_info["symbol"], task_info["market_type"]
                )
                batch_coroutines.append(coro)

            # 执行当前批次
            batch_start = time.time()
            results = await asyncio.gather(*batch_coroutines, return_exceptions=True)
            batch_duration = time.time() - batch_start

            # 统计结果
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            self.logger.info(f"✅ 第{batch_idx + 1}批完成: {success_count}/{len(batch_tasks)}成功, 耗时{batch_duration:.1f}秒")

            # 批间冷却（除了最后一批）
            if batch_idx < total_batches - 1:
                self.logger.info(f"🕐 批间冷却: {batch_cooldown}秒...")
                await asyncio.sleep(batch_cooldown)

        self.logger.info("✅ 分批预加载交易规则完成")

    async def batched_contract_info_fetch(self, exchanges: Dict[str, Any], symbols: List[str],
                                        batch_size: int, batch_cooldown: float):
        """🔥 新增：分批获取合约信息"""
        self.logger.info(f"📦 开始分批获取合约信息: 每批{batch_size}个，批间冷却{batch_cooldown}秒")

        # 创建合约信息任务
        contract_tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                if hasattr(exchange, 'get_contract_info'):
                    contract_tasks.append({
                        "exchange_name": exchange_name,
                        "exchange": exchange,
                        "symbol": symbol
                    })

        if not contract_tasks:
            self.logger.info("📋 无合约信息需要获取")
            return

        # 分批处理
        total_batches = (len(contract_tasks) + batch_size - 1) // batch_size
        self.logger.info(f"📊 合约信息任务数: {len(contract_tasks)}, 分为{total_batches}批")

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(contract_tasks))
            batch_tasks = contract_tasks[start_idx:end_idx]

            self.logger.info(f"🔄 处理合约信息第{batch_idx + 1}/{total_batches}批")

            # 并行执行当前批次
            batch_coroutines = []
            for task_info in batch_tasks:
                coro = self.rate_limited_api_call(
                    task_info["exchange_name"],
                    self._get_contract_info,
                    task_info["exchange"], task_info["symbol"]
                )
                batch_coroutines.append(coro)

            # 执行当前批次
            results = await asyncio.gather(*batch_coroutines, return_exceptions=True)
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            self.logger.info(f"✅ 合约信息第{batch_idx + 1}批完成: {success_count}/{len(batch_tasks)}成功")

            # 批间冷却
            if batch_idx < total_batches - 1:
                await asyncio.sleep(batch_cooldown)

        self.logger.info("✅ 分批获取合约信息完成")

    async def rate_limited_api_call(self, exchange_name: str, func, *args, **kwargs):
        """限速API调用"""
        # 添加到调用队列
        await self.call_queues[exchange_name].put((func, args, kwargs))
        
        # 等待执行
        return await self._execute_rate_limited_call(exchange_name)
        
    async def _execute_rate_limited_call(self, exchange_name: str):
        """🔥 修复版：执行限速调用 - 精确控制API调用间隔"""
        queue = self.call_queues[exchange_name]
        rate_limit = self.rate_limits[exchange_name]

        if queue.empty():
            return None

        func, args, kwargs = await queue.get()

        # 🔥 修复：精确的限速控制逻辑
        current_time = time.time()
        last_call_time = getattr(self, f"_{exchange_name}_last_call", 0)
        time_since_last = current_time - last_call_time
        min_interval = 1.0 / rate_limit

        # 🔥 关键修复：确保严格遵守最小间隔
        if time_since_last < min_interval:
            wait_time = min_interval - time_since_last
            self.logger.debug(f"🕐 {exchange_name} API限速等待: {wait_time:.3f}秒 (间隔要求: {min_interval:.3f}秒)")
            await asyncio.sleep(wait_time)

        # 🔥 修复：在执行调用前立即更新时间戳，确保精确间隔控制
        call_start_time = time.time()
        setattr(self, f"_{exchange_name}_last_call", call_start_time)

        # 执行调用
        try:
            result = await func(*args, **kwargs)
            call_duration = time.time() - call_start_time
            self.call_stats[exchange_name] += 1

            self.logger.debug(f"✅ {exchange_name} API调用成功，耗时: {call_duration:.3f}秒")
            return result

        except Exception as e:
            call_duration = time.time() - call_start_time
            error_msg = str(e)

            # 🔥 更智能的重试机制：限速错误特殊处理
            if "Too Many Requests" in error_msg or "50011" in error_msg or "10006" in error_msg:
                self.logger.warning(f"⚠️ {exchange_name} 触发限速错误，特殊延迟5秒: {error_msg}")
                await asyncio.sleep(5.0)  # 限速错误特殊延迟5秒

                # 指数退避重试
                for retry_count in range(3):
                    retry_delay = 2 ** retry_count  # 指数退避: 1秒, 2秒, 4秒
                    self.logger.info(f"🔄 {exchange_name} 限速错误重试 {retry_count + 1}/3，延迟{retry_delay}秒")
                    await asyncio.sleep(retry_delay)

                    try:
                        # 重新执行调用
                        result = await func(*args, **kwargs)
                        self.logger.info(f"✅ {exchange_name} 限速错误重试成功")
                        return result
                    except Exception as retry_e:
                        if retry_count == 2:  # 最后一次重试
                            self.logger.error(f"❌ {exchange_name} 限速错误重试失败: {retry_e}")
                        continue
            else:
                self.logger.error(f"❌ {exchange_name} API调用失败 (耗时: {call_duration:.3f}秒): {e}")

            # 🔥 失败的调用也要计入限速，避免因为失败而绕过限速控制
            return None
            
    async def batch_balance_queries(self, exchanges: Dict[str, Any]):
        """批量化余额查询"""
        self.logger.info("💰 批量化余额查询...")

        tasks = []
        for exchange_name, exchange in exchanges.items():
            if hasattr(exchange, 'get_balance'):
                task = self.rate_limited_api_call(
                    exchange_name,
                    self._get_balance,
                    exchange
                )
                tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        self.logger.info(f"✅ 余额查询完成: {success_count}/{len(tasks)}")

    async def smart_contract_info_fetch(self, exchanges: Dict[str, Any], symbols: List[str]):
        """智能化合约信息获取"""
        self.logger.info("📊 智能化合约信息获取...")

        tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                if hasattr(exchange, 'get_contract_info'):
                    task = self.rate_limited_api_call(
                        exchange_name,
                        self._get_contract_info,
                        exchange, symbol
                    )
                    tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        self.logger.info(f"✅ 合约信息获取完成: {success_count}/{len(tasks)}")

    async def delayed_leverage_setup(self, exchanges: Dict[str, Any], symbols: List[str]):
        """延迟杠杆设置"""
        self.logger.info("🔧 延迟杠杆设置...")

        # 延迟1秒再设置杠杆，避免与其他API调用冲突
        await asyncio.sleep(1)

        tasks = []
        for exchange_name, exchange in exchanges.items():
            for symbol in symbols:
                if hasattr(exchange, 'set_leverage'):
                    task = self.rate_limited_api_call(
                        exchange_name,
                        self._set_leverage,
                        exchange, symbol, 3  # 3倍杠杆
                    )
                    tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        self.logger.info(f"✅ 杠杆设置完成: {success_count}/{len(tasks)}")

    async def _get_trading_rule(self, exchange, symbol: str, market_type: str):
        """获取交易规则"""
        try:
            if hasattr(exchange, 'get_trading_rule'):
                return await exchange.get_trading_rule(symbol, market_type)
            return None
        except Exception as e:
            self.logger.debug(f"获取交易规则失败: {symbol} {market_type} - {e}")
            return None

    async def _get_balance(self, exchange):
        """获取余额"""
        try:
            # 使用现有的统一接口
            if hasattr(exchange, 'get_balance'):
                return await exchange.get_balance()
            return None
        except Exception as e:
            self.logger.debug(f"获取余额失败: {e}")
            return None

    async def _get_contract_info(self, exchange, symbol: str):
        """获取合约信息"""
        try:
            if hasattr(exchange, 'get_contract_info'):
                return await exchange.get_contract_info(symbol)
            return None
        except Exception as e:
            self.logger.debug(f"获取合约信息失败: {symbol} - {e}")
            return None

    def get_exchange_limits(self, exchange_name: str) -> Dict[str, Any]:
        """🔥 新增：获取交易所限速配置"""
        try:
            exchange_name = exchange_name.lower()
            
            # 返回交易所限速配置
            if exchange_name == "okx":
                return {
                    "requests_per_second": 1.5,  # 安全限制为1.5次/秒
                    "burst_limit": 3,
                    "backoff_factor": 2.0
                }
            elif exchange_name == "gate":
                return {
                    "requests_per_second": 2.0,
                    "burst_limit": 5,
                    "backoff_factor": 1.5
                }
            elif exchange_name == "bybit":
                return {
                    "requests_per_second": 5.0,
                    "burst_limit": 10,
                    "backoff_factor": 1.2
                }
            else:
                return {
                    "requests_per_second": 1.0,
                    "burst_limit": 2,
                    "backoff_factor": 2.0
                }
                
        except Exception as e:
            self.logger.error(f"获取交易所限速配置失败: {exchange_name} - {e}")
            return {}

    async def _set_leverage(self, exchange, symbol: str, leverage: int):
        """设置杠杆"""
        try:
            if hasattr(exchange, 'set_leverage'):
                return await exchange.set_leverage(symbol, leverage)
            return None
        except Exception as e:
            self.logger.debug(f"设置杠杆失败: {symbol} {leverage}x - {e}")
            return None

# 全局优化器实例
_api_optimizer = None

def get_api_optimizer():
    """获取API优化器实例"""
    global _api_optimizer
    if _api_optimizer is None:
        _api_optimizer = APICallOptimizer()
    return _api_optimizer

def get_api_call_optimizer():
    """获取API调用优化器实例（别名函数）"""
    return get_api_optimizer()
