#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔥 方案2: Gate.io交易对智能预验证系统

核心功能：
1. Gate.io交易对智能预验证
2. 缓存机制避免重复API调用  
3. 智能推荐系统提供替代方案
4. 性能评分系统
5. 与通用配置验证器集成

设计原则：
- 专门针对Gate.io交易所优化
- 智能缓存管理，减少API调用
- 提供详细的验证报告和建议
- 确保与其他交易所的兼容性检查
"""

import asyncio
import time
import logging
from typing import Dict, List, Set, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import json
import hashlib

logger = logging.getLogger(__name__)


class ValidationScore(Enum):
    """验证评分等级"""
    EXCELLENT = "excellent"      # 95-100分：完美支持
    GOOD = "good"               # 80-94分：良好支持
    ACCEPTABLE = "acceptable"   # 60-79分：可接受
    POOR = "poor"              # 40-59分：支持度差
    UNSUPPORTED = "unsupported" # 0-39分：不支持


@dataclass
class GateValidationResult:
    """Gate.io验证结果"""
    symbol: str
    spot_supported: bool
    futures_supported: bool
    score: int
    grade: ValidationScore
    details: Dict[str, Any]
    recommendations: List[str]
    alternatives: List[str]
    validation_time: float
    cached: bool = False


@dataclass  
class GateIntelligentReport:
    """Gate.io智能验证报告"""
    total_symbols: int
    validated_symbols: int
    excellent_symbols: List[str]
    good_symbols: List[str] 
    acceptable_symbols: List[str]
    poor_symbols: List[str]
    unsupported_symbols: List[str]
    recommendations: List[str]
    performance_summary: Dict[str, Any]
    validation_time: float


class GateIntelligentPreValidator:
    """
    🔥 Gate.io交易对智能预验证系统
    
    提供专门针对Gate.io的高级验证功能：
    - 智能缓存机制
    - 性能评分系统  
    - 智能推荐引擎
    - 兼容性检查
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 🔥 智能缓存系统
        self.validation_cache = {}  # symbol -> GateValidationResult
        self.cache_ttl = 1800  # 30分钟缓存TTL
        self.api_cache = {}     # API调用缓存
        self.api_cache_ttl = 600  # 10分钟API缓存
        
        # 🔥 Gate.io特定配置
        self.gate_specific_config = {
            "min_spot_volume": 1000.0,     # 最小现货日交易量
            "min_futures_volume": 5000.0,  # 最小期货日交易量  
            "required_precision": 6,        # 要求精度位数
            "max_spread_threshold": 0.002,  # 最大点差阈值
            "stability_score_weight": 0.3,  # 稳定性评分权重
            "liquidity_score_weight": 0.4,  # 流动性评分权重
            "compatibility_score_weight": 0.3  # 兼容性评分权重
        }
        
        # 🔥 智能推荐引擎数据
        self.popular_alternatives = {
            # 主流币种的推荐替代
            "BTC": ["BTC-USDT", "BTC_USDT"],
            "ETH": ["ETH-USDT", "ETH_USDT"], 
            "DOGE": ["DOGE-USDT", "DOGE_USDT"],
            "ADA": ["ADA-USDT", "ADA_USDT"],
            "MATIC": ["MATIC-USDT", "MATIC_USDT"],
            "DOT": ["DOT-USDT", "DOT_USDT"],
            "LINK": ["LINK-USDT", "LINK_USDT"],
            "UNI": ["UNI-USDT", "UNI_USDT"]
        }
        
        # 🔥 性能统计
        self.performance_stats = {
            "total_validations": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "api_calls": 0,
            "average_validation_time": 0.0,
            "validation_success_rate": 0.0
        }
        
    async def validate_symbol_intelligent(self, symbol: str, gate_exchange) -> GateValidationResult:
        """
        🔥 智能验证单个交易对
        
        Args:
            symbol: 标准格式交易对 (如 BTC-USDT)
            gate_exchange: Gate.io交易所实例
            
        Returns:
            GateValidationResult: 详细验证结果
        """
        start_time = time.time()
        
        try:
            # 🔥 检查智能缓存
            cache_key = self._generate_cache_key(symbol)
            cached_result = self._get_from_cache(cache_key)
            
            if cached_result:
                self.performance_stats["cache_hits"] += 1
                cached_result.cached = True
                self.logger.info(f"✅ Gate.io缓存命中: {symbol}")
                return cached_result
            
            self.performance_stats["cache_misses"] += 1
            self.logger.info(f"🔍 Gate.io智能验证开始: {symbol}")
            
            # 🔥 执行全面验证
            validation_result = await self._perform_comprehensive_validation(symbol, gate_exchange)
            
            # 🔥 计算智能评分
            score, grade = self._calculate_intelligent_score(validation_result)
            validation_result["score"] = score
            validation_result["grade"] = grade
            
            # 🔥 生成智能推荐
            recommendations = await self._generate_intelligent_recommendations(symbol, validation_result, gate_exchange)
            alternatives = self._find_intelligent_alternatives(symbol, validation_result)
            
            # 🔥 创建验证结果
            result = GateValidationResult(
                symbol=symbol,
                spot_supported=validation_result.get("spot_supported", False),
                futures_supported=validation_result.get("futures_supported", False),
                score=score,
                grade=grade,
                details=validation_result,
                recommendations=recommendations,
                alternatives=alternatives,
                validation_time=time.time() - start_time,
                cached=False
            )
            
            # 🔥 更新缓存
            self._update_cache(cache_key, result)
            
            # 🔥 更新性能统计
            self._update_performance_stats(result)
            
            self.logger.info(f"✅ Gate.io智能验证完成: {symbol} -> {grade.value} ({score}分)")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Gate.io智能验证失败: {symbol} - {e}")
            
            # 🔥 返回失败结果
            return GateValidationResult(
                symbol=symbol,
                spot_supported=False,
                futures_supported=False,
                score=0,
                grade=ValidationScore.UNSUPPORTED,
                details={"error": str(e)},
                recommendations=[f"验证失败: {e}"],
                alternatives=[],
                validation_time=time.time() - start_time,
                cached=False
            )
    
    async def validate_batch_intelligent(self, symbols: List[str], gate_exchange) -> GateIntelligentReport:
        """
        🔥 批量智能验证交易对
        
        Args:
            symbols: 交易对列表
            gate_exchange: Gate.io交易所实例
            
        Returns:
            GateIntelligentReport: 智能验证报告
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"🚀 Gate.io批量智能验证开始: {len(symbols)}个交易对")
            
            # 🔥 并发验证（使用信号量控制并发数）
            semaphore = asyncio.Semaphore(3)  # 限制同时3个验证任务
            
            async def validate_with_semaphore(symbol):
                async with semaphore:
                    return await self.validate_symbol_intelligent(symbol, gate_exchange)
            
            # 🔥 执行并发验证
            validation_tasks = [validate_with_semaphore(symbol) for symbol in symbols]
            results = await asyncio.gather(*validation_tasks, return_exceptions=True)
            
            # 🔥 分析验证结果
            excellent_symbols = []
            good_symbols = []
            acceptable_symbols = []
            poor_symbols = []
            unsupported_symbols = []
            
            successful_results = []
            
            for result in results:
                if isinstance(result, Exception):
                    self.logger.error(f"❌ 验证异常: {result}")
                    continue
                    
                if isinstance(result, GateValidationResult):
                    successful_results.append(result)
                    
                    if result.grade == ValidationScore.EXCELLENT:
                        excellent_symbols.append(result.symbol)
                    elif result.grade == ValidationScore.GOOD:
                        good_symbols.append(result.symbol)
                    elif result.grade == ValidationScore.ACCEPTABLE:
                        acceptable_symbols.append(result.symbol)
                    elif result.grade == ValidationScore.POOR:
                        poor_symbols.append(result.symbol)
                    else:
                        unsupported_symbols.append(result.symbol)
            
            # 🔥 生成全局推荐
            global_recommendations = self._generate_global_recommendations(successful_results)
            
            # 🔥 生成性能摘要
            performance_summary = self._generate_performance_summary(successful_results)
            
            # 🔥 创建智能报告
            report = GateIntelligentReport(
                total_symbols=len(symbols),
                validated_symbols=len(successful_results),
                excellent_symbols=excellent_symbols,
                good_symbols=good_symbols,
                acceptable_symbols=acceptable_symbols,
                poor_symbols=poor_symbols,
                unsupported_symbols=unsupported_symbols,
                recommendations=global_recommendations,
                performance_summary=performance_summary,
                validation_time=time.time() - start_time
            )
            
            self.logger.info(f"✅ Gate.io批量智能验证完成: {len(successful_results)}/{len(symbols)}成功")
            return report
            
        except Exception as e:
            self.logger.error(f"❌ Gate.io批量智能验证失败: {e}")
            
            # 🔥 返回空报告
            return GateIntelligentReport(
                total_symbols=len(symbols),
                validated_symbols=0,
                excellent_symbols=[],
                good_symbols=[],
                acceptable_symbols=[],
                poor_symbols=[],
                unsupported_symbols=symbols,
                recommendations=[f"批量验证失败: {e}"],
                performance_summary={},
                validation_time=time.time() - start_time
            )
    
    async def _perform_comprehensive_validation(self, symbol: str, gate_exchange) -> Dict[str, Any]:
        """
        🔥 执行全面验证
        """
        validation_data = {
            "symbol": symbol,
            "spot_supported": False,
            "futures_supported": False,
            "spot_details": {},
            "futures_details": {},
            "liquidity_score": 0,
            "stability_score": 0,
            "compatibility_score": 0
        }
        
        try:
            # 🔥 验证现货支持
            spot_validation = await self._validate_spot_support(symbol, gate_exchange)
            validation_data.update(spot_validation)
            
            # 🔥 验证期货支持  
            futures_validation = await self._validate_futures_support(symbol, gate_exchange)
            validation_data.update(futures_validation)
            
            # 🔥 计算流动性评分
            liquidity_score = await self._calculate_liquidity_score(symbol, gate_exchange, validation_data)
            validation_data["liquidity_score"] = liquidity_score
            
            # 🔥 计算稳定性评分
            stability_score = await self._calculate_stability_score(symbol, gate_exchange, validation_data)
            validation_data["stability_score"] = stability_score
            
            # 🔥 计算兼容性评分
            compatibility_score = self._calculate_compatibility_score(symbol, validation_data)
            validation_data["compatibility_score"] = compatibility_score
            
            return validation_data
            
        except Exception as e:
            self.logger.error(f"❌ 全面验证失败: {symbol} - {e}")
            validation_data["error"] = str(e)
            return validation_data
    
    async def _validate_spot_support(self, symbol: str, gate_exchange) -> Dict[str, Any]:
        """
        🔥 验证Gate.io现货支持度
        """
        result = {
            "spot_supported": False,
            "spot_details": {}
        }
        
        try:
            # 🔥 获取现货交易对信息（使用缓存）
            spot_pairs = await self._get_cached_api_data("spot_pairs", gate_exchange.get_currency_pairs)
            
            if not spot_pairs:
                return result
            
            # 🔥 转换为Gate.io格式
            gate_symbol = symbol.replace('-', '_')  # BTC-USDT -> BTC_USDT
            
            # 🔥 查找匹配的交易对
            matching_pair = None
            for pair in spot_pairs:
                if pair.get("id") == gate_symbol:
                    matching_pair = pair
                    break
            
            if matching_pair:
                result["spot_supported"] = True
                result["spot_details"] = {
                    "id": matching_pair.get("id"),
                    "base": matching_pair.get("base"),
                    "quote": matching_pair.get("quote"),
                    "fee": float(matching_pair.get("fee", "0.002")),
                    "min_base_amount": float(matching_pair.get("min_base_amount", "0")),
                    "min_quote_amount": float(matching_pair.get("min_quote_amount", "0")),
                    "amount_precision": int(matching_pair.get("amount_precision", 8)),
                    "precision": int(matching_pair.get("precision", 8)),
                    "trade_status": matching_pair.get("trade_status", "tradable")
                }
                self.logger.info(f"✅ Gate.io现货支持: {symbol} -> {gate_symbol}")
            else:
                self.logger.warning(f"⚠️ Gate.io现货不支持: {symbol}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 验证Gate.io现货支持失败: {symbol} - {e}")
            result["spot_details"]["error"] = str(e)
            return result
    
    async def _validate_futures_support(self, symbol: str, gate_exchange) -> Dict[str, Any]:
        """
        🔥 验证Gate.io期货支持度
        """
        result = {
            "futures_supported": False,
            "futures_details": {}
        }
        
        try:
            # 🔥 获取期货合约信息（使用缓存）
            futures_contracts = await self._get_cached_api_data("futures_contracts", 
                                                              lambda: gate_exchange.get_futures_contracts("usdt"))
            
            if not futures_contracts:
                return result
            
            # 🔥 转换为Gate.io期货格式
            gate_futures_symbol = symbol.replace('-', '_')  # BTC-USDT -> BTC_USDT
            
            # 🔥 查找匹配的合约
            matching_contract = None
            for contract in futures_contracts:
                if contract.get("name") == gate_futures_symbol:
                    matching_contract = contract
                    break
            
            if matching_contract:
                result["futures_supported"] = True
                result["futures_details"] = {
                    "name": matching_contract.get("name"),
                    "type": matching_contract.get("type"),
                    "quanto_multiplier": float(matching_contract.get("quanto_multiplier", "0.0001")),
                    "leverage_min": float(matching_contract.get("leverage_min", "1")),
                    "leverage_max": float(matching_contract.get("leverage_max", "100")),
                    "maintenance_rate": float(matching_contract.get("maintenance_rate", "0.005")),
                    "mark_type": matching_contract.get("mark_type"),
                    "last_price": float(matching_contract.get("last_price", "0")),
                    "mark_price": float(matching_contract.get("mark_price", "0")),
                    "index_price": float(matching_contract.get("index_price", "0")),
                    "funding_rate": float(matching_contract.get("funding_rate", "0")),
                    "funding_interval": int(matching_contract.get("funding_interval", 28800)),
                    "order_size_min": int(matching_contract.get("order_size_min", 1)),
                    "order_size_max": int(matching_contract.get("order_size_max", 1000000)),
                    "trade_status": matching_contract.get("in_delisting", False)
                }
                self.logger.info(f"✅ Gate.io期货支持: {symbol} -> {gate_futures_symbol}")
            else:
                self.logger.warning(f"⚠️ Gate.io期货不支持: {symbol}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 验证Gate.io期货支持失败: {symbol} - {e}")
            result["futures_details"]["error"] = str(e)
            return result
    
    async def _calculate_liquidity_score(self, symbol: str, gate_exchange, validation_data: Dict) -> int:
        """
        🔥 计算流动性评分 (0-100)
        """
        try:
            score = 0
            
            # 🔥 现货流动性评分 (50分)
            if validation_data.get("spot_supported"):
                spot_details = validation_data.get("spot_details", {})
                
                # 最小交易金额越低越好 (25分)
                min_quote_amount = float(spot_details.get("min_quote_amount", 100))
                if min_quote_amount <= 5:
                    score += 25
                elif min_quote_amount <= 10:
                    score += 20
                elif min_quote_amount <= 20:
                    score += 15
                elif min_quote_amount <= 50:
                    score += 10
                else:
                    score += 5
                
                # 精度支持 (25分)
                amount_precision = int(spot_details.get("amount_precision", 0))
                if amount_precision >= 8:
                    score += 25
                elif amount_precision >= 6:
                    score += 20
                elif amount_precision >= 4:
                    score += 15
                else:
                    score += 10
            
            # 🔥 期货流动性评分 (50分)
            if validation_data.get("futures_supported"):
                futures_details = validation_data.get("futures_details", {})
                
                # 最小订单量越低越好 (25分)
                order_size_min = int(futures_details.get("order_size_min", 100))
                if order_size_min <= 1:
                    score += 25
                elif order_size_min <= 10:
                    score += 20
                elif order_size_min <= 50:
                    score += 15
                else:
                    score += 10
                
                # 杠杆支持度 (25分)
                leverage_max = float(futures_details.get("leverage_max", 1))
                if leverage_max >= 100:
                    score += 25
                elif leverage_max >= 50:
                    score += 20
                elif leverage_max >= 20:
                    score += 15
                else:
                    score += 10
            
            return min(score, 100)
            
        except Exception as e:
            self.logger.error(f"❌ 计算流动性评分失败: {symbol} - {e}")
            return 0
    
    async def _calculate_stability_score(self, symbol: str, gate_exchange, validation_data: Dict) -> int:
        """
        🔥 计算稳定性评分 (0-100)
        """
        try:
            score = 0
            
            # 🔥 基础稳定性检查 (60分)
            if validation_data.get("spot_supported"):
                spot_details = validation_data.get("spot_details", {})
                trade_status = spot_details.get("trade_status", "")
                
                if trade_status == "tradable":
                    score += 30
                elif trade_status in ["buyable", "sellable"]:
                    score += 20
                else:
                    score += 10
            
            if validation_data.get("futures_supported"):
                futures_details = validation_data.get("futures_details", {})
                in_delisting = futures_details.get("trade_status", False)
                
                if not in_delisting:
                    score += 30
                else:
                    score += 10
            
            # 🔥 价格稳定性检查 (40分)
            try:
                # 检查现货和期货价格一致性
                if validation_data.get("spot_supported") and validation_data.get("futures_supported"):
                    futures_details = validation_data.get("futures_details", {})
                    mark_price = float(futures_details.get("mark_price", 0))
                    index_price = float(futures_details.get("index_price", 0))
                    
                    if mark_price > 0 and index_price > 0:
                        price_diff_pct = abs(mark_price - index_price) / index_price * 100
                        
                        if price_diff_pct <= 0.1:  # 0.1%以内
                            score += 40
                        elif price_diff_pct <= 0.5:  # 0.5%以内
                            score += 30
                        elif price_diff_pct <= 1.0:  # 1%以内
                            score += 20
                        else:
                            score += 10
                    else:
                        score += 20  # 无价格数据，给予中等分数
                else:
                    score += 30  # 单一市场，给予基础分数
                    
            except Exception:
                score += 20  # 价格检查失败，给予保守分数
            
            return min(score, 100)
            
        except Exception as e:
            self.logger.error(f"❌ 计算稳定性评分失败: {symbol} - {e}")
            return 0
    
    def _calculate_compatibility_score(self, symbol: str, validation_data: Dict) -> int:
        """
        🔥 计算兼容性评分 (0-100)
        """
        try:
            score = 0
            
            # 🔥 基础兼容性 (40分)
            if validation_data.get("spot_supported"):
                score += 20
            if validation_data.get("futures_supported"):
                score += 20
            
            # 🔥 格式兼容性 (30分)
            # 检查是否为标准USDT交易对
            if symbol.endswith("-USDT"):
                score += 30
            elif symbol.endswith("USDT"):
                score += 20
            else:
                score += 10
            
            # 🔥 主流币种加分 (30分)
            base_currency = symbol.split('-')[0] if '-' in symbol else symbol[:-4]
            mainstream_coins = ["BTC", "ETH", "DOGE", "ADA", "MATIC", "DOT", "LINK", "UNI", "SOL", "AVAX"]
            
            if base_currency in mainstream_coins:
                score += 30
            elif len(base_currency) <= 5:  # 短名称通常更主流
                score += 20
            else:
                score += 10
            
            return min(score, 100)
            
        except Exception as e:
            self.logger.error(f"❌ 计算兼容性评分失败: {symbol} - {e}")
            return 0
    
    def _calculate_intelligent_score(self, validation_data: Dict) -> Tuple[int, ValidationScore]:
        """
        🔥 计算智能总评分和等级
        """
        try:
            # 🔥 加权平均计算总分
            liquidity_score = validation_data.get("liquidity_score", 0)
            stability_score = validation_data.get("stability_score", 0)
            compatibility_score = validation_data.get("compatibility_score", 0)
            
            config = self.gate_specific_config
            total_score = (
                liquidity_score * config["liquidity_score_weight"] +
                stability_score * config["stability_score_weight"] + 
                compatibility_score * config["compatibility_score_weight"]
            )
            
            total_score = int(round(total_score))
            
            # 🔥 确定等级
            if total_score >= 95:
                grade = ValidationScore.EXCELLENT
            elif total_score >= 80:
                grade = ValidationScore.GOOD
            elif total_score >= 60:
                grade = ValidationScore.ACCEPTABLE
            elif total_score >= 40:
                grade = ValidationScore.POOR
            else:
                grade = ValidationScore.UNSUPPORTED
            
            return total_score, grade
            
        except Exception as e:
            self.logger.error(f"❌ 计算智能评分失败: {e}")
            return 0, ValidationScore.UNSUPPORTED
    
    async def _generate_intelligent_recommendations(self, symbol: str, validation_data: Dict, gate_exchange) -> List[str]:
        """
        🔥 生成智能推荐
        """
        recommendations = []
        
        try:
            score = validation_data.get("score", 0)
            
            # 🔥 基于评分生成建议
            if score >= 95:
                recommendations.append(f"{symbol} 完美支持，推荐优先使用")
            elif score >= 80:
                recommendations.append(f"{symbol} 良好支持，推荐使用")
            elif score >= 60:
                recommendations.append(f"{symbol} 可以使用，但需要注意流动性")
            elif score >= 40:
                recommendations.append(f"{symbol} 支持度较差，建议谨慎使用")
            else:
                recommendations.append(f"{symbol} 不建议使用，寻找替代方案")
            
            # 🔥 具体问题的建议
            if not validation_data.get("spot_supported"):
                recommendations.append("当前不支持现货交易，无法进行期现套利")
            elif not validation_data.get("futures_supported"):
                recommendations.append("当前不支持期货交易，无法进行期现套利")
            
            # 🔥 流动性建议
            liquidity_score = validation_data.get("liquidity_score", 0)
            if liquidity_score < 50:
                recommendations.append("流动性较低，建议增加滑点容忍度")
            
            # 🔥 稳定性建议
            stability_score = validation_data.get("stability_score", 0)
            if stability_score < 60:
                recommendations.append("价格稳定性有问题，建议加强风控")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"❌ 生成智能推荐失败: {symbol} - {e}")
            return [f"推荐生成失败: {e}"]
    
    def _find_intelligent_alternatives(self, symbol: str, validation_data: Dict) -> List[str]:
        """
        🔥 寻找智能替代方案
        """
        alternatives = []
        
        try:
            base_currency = symbol.split('-')[0] if '-' in symbol else symbol[:-4]
            
            # 🔥 从预定义的替代方案中查找
            if base_currency in self.popular_alternatives:
                alternatives.extend(self.popular_alternatives[base_currency])
            
            # 🔥 如果当前交易对有问题，生成可能的替代格式
            if validation_data.get("score", 0) < 60:
                if '-' in symbol:
                    # 尝试下划线格式
                    alt_symbol = symbol.replace('-', '_')
                    if alt_symbol != symbol:
                        alternatives.append(alt_symbol)
                elif '_' in symbol:
                    # 尝试连字符格式
                    alt_symbol = symbol.replace('_', '-')
                    if alt_symbol != symbol:
                        alternatives.append(alt_symbol)
                
                # 🔥 生成无分隔符格式
                clean_symbol = symbol.replace('-', '').replace('_', '')
                if clean_symbol != symbol:
                    alternatives.append(clean_symbol)
            
            # 🔥 去重和验证
            alternatives = list(set(alternatives))
            alternatives = [alt for alt in alternatives if alt != symbol]
            
            return alternatives[:5]  # 最多返回5个替代方案
            
        except Exception as e:
            self.logger.error(f"❌ 寻找智能替代方案失败: {symbol} - {e}")
            return []
    
    def _generate_global_recommendations(self, results: List[GateValidationResult]) -> List[str]:
        """
        🔥 生成全局推荐
        """
        recommendations = []
        
        try:
            if not results:
                return ["没有验证结果可分析"]
            
            # 🔥 统计分析
            total_symbols = len(results)
            excellent_count = sum(1 for r in results if r.grade == ValidationScore.EXCELLENT)
            good_count = sum(1 for r in results if r.grade == ValidationScore.GOOD)
            unsupported_count = sum(1 for r in results if r.grade == ValidationScore.UNSUPPORTED)
            
            success_rate = (excellent_count + good_count) / total_symbols * 100
            
            # 🔥 生成全局建议
            if success_rate >= 80:
                recommendations.append(f"总体验证成功率{success_rate:.1f}%，配置质量优秀")
            elif success_rate >= 60:
                recommendations.append(f"总体验证成功率{success_rate:.1f}%，配置质量良好")
            else:
                recommendations.append(f"总体验证成功率{success_rate:.1f}%，建议优化配置")
            
            if unsupported_count > 0:
                recommendations.append(f"发现{unsupported_count}个不支持的交易对，建议移除或替换")
            
            # 🔥 性能优化建议
            avg_validation_time = sum(r.validation_time for r in results) / len(results)
            if avg_validation_time > 2.0:
                recommendations.append("验证耗时较长，建议启用更积极的缓存策略")
            
            cache_hit_rate = sum(1 for r in results if r.cached) / len(results) * 100
            if cache_hit_rate < 50:
                recommendations.append(f"缓存命中率{cache_hit_rate:.1f}%，建议调整缓存策略")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"❌ 生成全局推荐失败: {e}")
            return [f"全局推荐生成失败: {e}"]
    
    def _generate_performance_summary(self, results: List[GateValidationResult]) -> Dict[str, Any]:
        """
        🔥 生成性能摘要
        """
        try:
            if not results:
                return {}
            
            total_time = sum(r.validation_time for r in results)
            avg_time = total_time / len(results)
            cached_count = sum(1 for r in results if r.cached)
            cache_rate = cached_count / len(results) * 100
            
            scores = [r.score for r in results]
            avg_score = sum(scores) / len(scores)
            
            return {
                "total_validation_time": round(total_time, 3),
                "average_validation_time": round(avg_time, 3),
                "cache_hit_rate": round(cache_rate, 1),
                "average_score": round(avg_score, 1),
                "max_score": max(scores),
                "min_score": min(scores),
                "api_calls_made": self.performance_stats["api_calls"]
            }
            
        except Exception as e:
            self.logger.error(f"❌ 生成性能摘要失败: {e}")
            return {"error": str(e)}
    
    async def _get_cached_api_data(self, key: str, api_func) -> Any:
        """
        🔥 获取缓存的API数据
        """
        try:
            current_time = time.time()
            
            # 🔥 检查API缓存
            if key in self.api_cache:
                data, cache_time = self.api_cache[key]
                if current_time - cache_time < self.api_cache_ttl:
                    return data
            
            # 🔥 调用API获取数据
            self.performance_stats["api_calls"] += 1
            data = await api_func()
            
            # 🔥 更新缓存
            self.api_cache[key] = (data, current_time)
            
            return data
            
        except Exception as e:
            self.logger.error(f"❌ 获取缓存API数据失败: {key} - {e}")
            return None
    
    def _generate_cache_key(self, symbol: str) -> str:
        """
        🔥 生成缓存键
        """
        # 包含时间戳确保定期刷新
        time_bucket = int(time.time() // self.cache_ttl)
        return hashlib.md5(f"{symbol}_{time_bucket}".encode()).hexdigest()
    
    def _get_from_cache(self, cache_key: str) -> Optional[GateValidationResult]:
        """
        🔥 从缓存获取结果
        """
        try:
            if cache_key in self.validation_cache:
                result, cache_time = self.validation_cache[cache_key]
                if time.time() - cache_time < self.cache_ttl:
                    return result
                else:
                    # 🔥 清理过期缓存
                    del self.validation_cache[cache_key]
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 从缓存获取失败: {e}")
            return None
    
    def _update_cache(self, cache_key: str, result: GateValidationResult):
        """
        🔥 更新缓存
        """
        try:
            self.validation_cache[cache_key] = (result, time.time())
            
            # 🔥 清理过期缓存（如果缓存过多）
            if len(self.validation_cache) > 1000:
                self._cleanup_expired_cache()
                
        except Exception as e:
            self.logger.error(f"❌ 更新缓存失败: {e}")
    
    def _cleanup_expired_cache(self):
        """
        🔥 清理过期缓存
        """
        try:
            current_time = time.time()
            expired_keys = []
            
            for key, (result, cache_time) in self.validation_cache.items():
                if current_time - cache_time >= self.cache_ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.validation_cache[key]
            
            self.logger.info(f"🧹 清理过期缓存: {len(expired_keys)}个")
            
        except Exception as e:
            self.logger.error(f"❌ 清理缓存失败: {e}")
    
    def _update_performance_stats(self, result: GateValidationResult):
        """
        🔥 更新性能统计
        """
        try:
            self.performance_stats["total_validations"] += 1
            
            # 🔥 更新平均验证时间
            total_time = (
                self.performance_stats["average_validation_time"] * 
                (self.performance_stats["total_validations"] - 1) + 
                result.validation_time
            )
            self.performance_stats["average_validation_time"] = total_time / self.performance_stats["total_validations"]
            
            # 🔥 更新成功率
            if result.score >= 60:  # 可接受以上算成功
                self.performance_stats["validation_success_rate"] = (
                    self.performance_stats.get("successful_validations", 0) + 1
                ) / self.performance_stats["total_validations"] * 100
                self.performance_stats["successful_validations"] = self.performance_stats.get("successful_validations", 0) + 1
            
        except Exception as e:
            self.logger.error(f"❌ 更新性能统计失败: {e}")
    
    def print_intelligent_report(self, report: GateIntelligentReport):
        """
        🔥 打印智能验证报告
        """
        print("\n" + "="*80)
        print("🚀 Gate.io 交易对智能预验证报告")
        print("="*80)
        
        print(f"📊 验证摘要:")
        print(f"   总交易对: {report.total_symbols}")
        print(f"   验证成功: {report.validated_symbols}")
        print(f"   验证耗时: {report.validation_time:.2f}秒")
        
        print(f"\n🏆 评分分布:")
        print(f"   优秀 (95-100分): {len(report.excellent_symbols)}个")
        if report.excellent_symbols:
            print(f"      {', '.join(report.excellent_symbols[:5])}{'...' if len(report.excellent_symbols) > 5 else ''}")
        
        print(f"   良好 (80-94分): {len(report.good_symbols)}个")
        if report.good_symbols:
            print(f"      {', '.join(report.good_symbols[:5])}{'...' if len(report.good_symbols) > 5 else ''}")
        
        print(f"   可接受 (60-79分): {len(report.acceptable_symbols)}个")
        if report.acceptable_symbols:
            print(f"      {', '.join(report.acceptable_symbols[:5])}{'...' if len(report.acceptable_symbols) > 5 else ''}")
        
        print(f"   较差 (40-59分): {len(report.poor_symbols)}个")
        if report.poor_symbols:
            print(f"      {', '.join(report.poor_symbols[:5])}{'...' if len(report.poor_symbols) > 5 else ''}")
        
        print(f"   不支持 (0-39分): {len(report.unsupported_symbols)}个")
        if report.unsupported_symbols:
            print(f"      {', '.join(report.unsupported_symbols[:5])}{'...' if len(report.unsupported_symbols) > 5 else ''}")
        
        if report.recommendations:
            print(f"\n💡 智能建议:")
            for i, rec in enumerate(report.recommendations, 1):
                print(f"   {i}. {rec}")
        
        if report.performance_summary:
            print(f"\n⚡ 性能摘要:")
            perf = report.performance_summary
            print(f"   平均验证时间: {perf.get('average_validation_time', 0):.3f}秒")
            print(f"   缓存命中率: {perf.get('cache_hit_rate', 0):.1f}%")
            print(f"   平均评分: {perf.get('average_score', 0):.1f}分")
            print(f"   API调用次数: {perf.get('api_calls_made', 0)}")
        
        print("\n" + "="*80)


# 🔥 全局实例
_gate_intelligent_validator = None


def get_gate_intelligent_pre_validator() -> GateIntelligentPreValidator:
    """获取Gate.io智能预验证器实例"""
    global _gate_intelligent_validator
    if _gate_intelligent_validator is None:
        _gate_intelligent_validator = GateIntelligentPreValidator()
    return _gate_intelligent_validator


async def validate_gate_config_intelligent(gate_exchange, configured_symbols: List[str]) -> GateIntelligentReport:
    """
    🔥 验证Gate.io配置（智能版本）
    
    Args:
        gate_exchange: Gate.io交易所实例
        configured_symbols: 配置的交易对列表
        
    Returns:
        GateIntelligentReport: 智能验证报告
    """
    validator = get_gate_intelligent_pre_validator()
    return await validator.validate_batch_intelligent(configured_symbols, gate_exchange)