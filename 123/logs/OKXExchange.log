2025-08-03 13:32:34 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-03 13:32:34 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-03 13:32:34 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-03 13:32:34 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-03 13:32:34.923 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制已统一为2次/秒（确保三交易所一致性）
2025-08-03 13:32:34.923 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-08-03 13:32:34 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-03 13:32:35 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-03 13:32:35 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-03 13:32:35 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-03 13:32:35.372 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:32:35.716 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:32:35.716 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:32:36.032 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:32:36.032 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:32:36 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-08-03 13:32:36.032 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:32:36.360 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:32:36.360 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:32:36.700 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:32:36.700 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:32:36 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-08-03 13:32:36.700 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:32:37.025 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:32:37.025 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:32:37.354 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:32:37.355 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:32:37 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-08-03 13:32:37.355 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:32:37.698 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:32:37.698 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:32:38.011 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:32:38.011 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:32:38 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-08-03 13:32:38 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-03 13:32:38.336 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 13:32:39.723 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 13:32:41.226 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:32:41.890 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:32:42.970 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:32:45.051 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:32:49.125 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:32:50.202 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:32:50.774 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:32:51.852 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:32:53.958 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:32:58.032 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:33:03.784 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:33:03.784 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:33:04.456 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:33:04.456 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:33:05.143 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:33:05.144 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:33:05.782 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:33:05.782 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:33:06.440 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:33:06.440 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:33:07.113 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:33:07.114 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:33:53.567 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 13:33:54.068 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:33:54.069 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-08-03 13:33:54.070 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 13:33:54.073 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:33:56.480 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-03 13:33:56.981 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-08-03 13:33:56.989 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-03 13:33:56.993 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-08-03 13:33:56.997 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-03 13:33:58.000 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:33:58.416 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:33:58.416 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:33:58.502 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:33:58.502 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:33:58.502 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:33:58.502 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:33:58.502 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:33:58.502 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:33:58.502 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:33:58.502 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:33:58.503 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 13:33:58.740 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:33:58.740 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:33:58.912 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:33:58.913 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:33:58.914 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:33:58.914 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:33:58.922 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:33:58.922 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:33:58.929 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 13:33:58.930 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 13:33:58.930 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 13:33:58.930 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 13:33:58.930 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 13:33:58.931 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 13:33:58.931 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 13:33:58.931 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 13:33:58 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 13:33:58.933 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 13:33:58.933 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 13:33:58 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 13:33:58.939 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 13:33:58 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 13:33:58.941 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 13:33:58.941 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 13:33:58.941 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 13:33:58.941 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 13:33:58.941 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 13:33:58.942 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 13:33:58.942 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 13:33:58.942 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 13:33:58 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 13:33:58.949 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 13:33:58 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 13:33:58.955 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:33:58.955 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:33:59.242 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:33:59.243 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:33:59.244 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:33:59.244 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:33:59.251 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:33:59.251 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:33:59.255 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:33:59.255 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:34:01.014 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:34:01.015 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:34:01.340 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:34:01.340 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:34:01.342 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:34:01.343 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:34:01.345 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:34:01.345 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:34:01.350 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 13:34:01.350 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 13:34:01.364 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:34:01.364 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:34:01.667 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:34:01.667 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:34:01.669 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:34:01.669 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:34:01.678 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:34:01.678 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:34:01.695 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 13:34:01.695 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 13:34:17.158 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 13:34:18.662 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 13:34:20.157 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:34:20.826 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:34:21.908 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:34:23.977 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:34:28.057 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:34:28.388 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:34:28.957 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:34:30.024 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:34:32.093 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:34:36.167 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:34:40.843 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 13:34:42.355 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 13:34:43.842 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:34:44.509 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:34:45.577 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:34:47.646 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:34:51.717 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 13:34:52.066 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:34:52.646 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:34:53.727 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:34:55.796 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 13:34:59.884 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
