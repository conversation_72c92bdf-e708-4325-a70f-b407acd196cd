2025-08-03 13:32:49.125 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 13:32:58.032 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 13:33:58.929 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 13:33:58.930 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 13:33:58.930 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 13:33:58.930 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 13:33:58.930 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 13:33:58.931 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 13:33:58.931 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 13:33:58.931 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 13:33:58.932 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 13:33:58.933 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 13:33:58.933 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 13:33:58.939 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 13:33:58.940 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 13:33:58.941 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 13:33:58.941 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 13:33:58.941 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 13:33:58.941 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 13:33:58.941 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 13:33:58.942 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 13:33:58.942 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 13:33:58.942 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 13:33:58.949 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 13:33:58.950 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 13:34:00.177 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-03 13:34:00.177 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-03 13:34:00.177 [ERROR] [exchanges.bybit_exchange] ❌ Bybit设置杠杆异常: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-08-03 13:34:09.258 [ERROR] [websocket.manager] 初始化gate期货WebSocket客户端失败: 'WebSocketManager' object has no attribute 'connection_pool_manager'
2025-08-03 13:34:09.259 [ERROR] [websocket.manager] 初始化bybit期货WebSocket客户端失败: 'WebSocketManager' object has no attribute 'connection_pool_manager'
2025-08-03 13:34:09.259 [ERROR] [websocket.manager] 初始化okx期货WebSocket客户端失败: 'WebSocketManager' object has no attribute 'connection_pool_manager'
2025-08-03 13:34:11.940 [ERROR] [core.trading_system_initializer] ❌ WebSocket初始化失败: 'WebSocketManager' object has no attribute 'connection_pool_manager'
2025-08-03 13:34:11.941 [ERROR] [websocket.manager] 初始化gate期货WebSocket客户端失败: 'WebSocketManager' object has no attribute 'connection_pool_manager'
2025-08-03 13:34:11.941 [ERROR] [websocket.manager] 初始化bybit期货WebSocket客户端失败: 'WebSocketManager' object has no attribute 'connection_pool_manager'
2025-08-03 13:34:11.942 [ERROR] [websocket.manager] 初始化okx期货WebSocket客户端失败: 'WebSocketManager' object has no attribute 'connection_pool_manager'
2025-08-03 13:34:12.346 [ERROR] [websocket] [GATE] ❌ [GATE-SPOT] 订阅失败: {'time': 1754220852, 'time_ms': 1754220852286, 'conn_id': '67e444a85700457c', 'trace_id': 'f6195b10b0c99a556adad4f663631598', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': 'f6195b10b0c99a556adad4f663631598'}
2025-08-03 13:34:12.346 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'spot.order_book', 'market_type': 'spot', 'error_message': "{'time': 1754220852, 'time_ms': 1754220852286, 'conn_id': '67e444a85700457c', 'trace_id': 'f6195b10b0c99a556adad4f663631598', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': 'f6195b10b0c99a556adad4f663631598'}"}
2025-08-03 13:34:14.861 [ERROR] [websocket] [GATE] ❌ [GATE-SPOT] 订阅失败: {'time': 1754220854, 'time_ms': 1754220854800, 'conn_id': 'c45b3cd941f35300', 'trace_id': '9487554a28053f4d28d2b1a2e8ada56a', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '9487554a28053f4d28d2b1a2e8ada56a'}
2025-08-03 13:34:14.861 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'spot.order_book', 'market_type': 'spot', 'error_message': "{'time': 1754220854, 'time_ms': 1754220854800, 'conn_id': 'c45b3cd941f35300', 'trace_id': '9487554a28053f4d28d2b1a2e8ada56a', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '9487554a28053f4d28d2b1a2e8ada56a'}"}
2025-08-03 13:34:28.058 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 13:34:36.167 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 13:34:51.718 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 13:34:59.885 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-08-03 13:35:06.177 [ERROR] [OpportunityScanner] 🚨 健康检查告警: Bybit期货数据键为0
2025-08-03 13:35:06.178 [ERROR] [OpportunityScanner] 🚨 这将导致套利机会检测失败
2025-08-03 13:35:06.178 [ERROR] [OpportunityScanner] 📊 当前数据状态: 总键数=24, Bybit期货键数=0
2025-08-03 13:35:08.507 [ERROR] [websocket.manager] 初始化gate期货WebSocket客户端失败: 'WebSocketManager' object has no attribute 'connection_pool_manager'
2025-08-03 13:35:08.508 [ERROR] [websocket.manager] 初始化bybit期货WebSocket客户端失败: 'WebSocketManager' object has no attribute 'connection_pool_manager'
2025-08-03 13:35:08.509 [ERROR] [websocket.manager] 初始化okx期货WebSocket客户端失败: 'WebSocketManager' object has no attribute 'connection_pool_manager'
2025-08-03 13:35:10.875 [ERROR] [ArbitrageEngine] ❌ WebSocket回调注册失败: 'WebSocketManager' object has no attribute 'connection_pool_manager'
2025-08-03 13:35:11.355 [ERROR] [websocket] [GATE] ❌ [GATE-SPOT] 订阅失败: {'time': 1754220911, 'time_ms': 1754220911292, 'conn_id': '676f4ccbb6031588', 'trace_id': '8ff2af2af91cd2adf8fe35a159fad44a', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '8ff2af2af91cd2adf8fe35a159fad44a'}
2025-08-03 13:35:11.355 [ERROR] [websocket_subscription_failure] [gate] 订阅响应失败 | {'channel': 'spot.order_book', 'market_type': 'spot', 'error_message': "{'time': 1754220911, 'time_ms': 1754220911292, 'conn_id': '676f4ccbb6031588', 'trace_id': '8ff2af2af91cd2adf8fe35a159fad44a', 'channel': 'spot.order_book', 'event': 'subscribe', 'payload': ['MATIC_USDT', '10', '100ms'], 'error': {'code': 2, 'message': 'unknown currency pair MATIC_USDT'}, 'result': {'status': 'fail'}, 'requestId': '8ff2af2af91cd2adf8fe35a159fad44a'}"}
