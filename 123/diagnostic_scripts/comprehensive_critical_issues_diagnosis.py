#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 综合关键问题诊断脚本
基于严重问题!.md文档和实际代码审查，精确诊断所有关键问题

作者: <PERSON> Code Assistant
日期: 2025-08-03
版本: v1.0 - 综合诊断版
"""

import sys
import os
import json
import ast
import re
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class CriticalIssuesDiagnosisEngine:
    """关键问题诊断引擎"""
    
    def __init__(self):
        self.project_root = project_root
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "diagnosis_summary": {},
            "critical_bugs": {},
            "system_issues": {},
            "config_inconsistencies": {},
            "recommendations": []
        }
        
    def run_comprehensive_diagnosis(self) -> Dict[str, Any]:
        """运行综合诊断"""
        print("🔍 开始综合关键问题诊断...")
        
        # 诊断关键Bug
        self._diagnose_bug5_error_handler_stats()
        self._diagnose_bug6_websocket_null_pointer()
        self._diagnose_bug7_timestamp_sync()
        
        # 诊断系统问题
        self._diagnose_api_config_inconsistency()
        self._diagnose_contract_info_failures()
        self._diagnose_trading_pair_validation()
        
        # 诊断WebSocket连接问题
        self._diagnose_websocket_connection_issues()
        
        # 生成综合总结
        self._generate_diagnosis_summary()
        
        return self.results
    
    def _diagnose_bug5_error_handler_stats(self):
        """诊断Bug5: 错误处理器统计计算错误"""
        print("\n🔍 诊断Bug5: 错误处理器统计计算错误...")
        
        try:
            error_handler_path = self.project_root / "websocket" / "error_handler.py"
            if not error_handler_path.exists():
                self.results["critical_bugs"]["bug5"] = {
                    "status": "FILE_NOT_FOUND",
                    "severity": "CRITICAL",
                    "message": "error_handler.py文件不存在"
                }
                return
            
            with open(error_handler_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查第289-290行的错误代码
            lines = content.split('\n')
            
            issues_found = []
            
            # 查找problematic pattern
            problematic_patterns = [
                r'sum\(len\(\[.*?\]\)\)',  # sum(len([...]))模式
                r'total_attempts\s*=\s*sum\(len\(',  # 具体的错误行
                r'successful_recoveries\s*=\s*sum\(len\('  # 具体的错误行
            ]
            
            for i, line in enumerate(lines, 1):
                for pattern in problematic_patterns:
                    if re.search(pattern, line):
                        issues_found.append({
                            "line_number": i,
                            "code": line.strip(),
                            "issue": "TypeError: 'int' object is not iterable",
                            "explanation": "len()返回整数，对整数使用sum()会导致TypeError"
                        })
            
            self.results["critical_bugs"]["bug5"] = {
                "status": "CONFIRMED" if issues_found else "NOT_FOUND",
                "severity": "CRITICAL",
                "file_path": str(error_handler_path),
                "issues_found": issues_found,
                "fix_required": len(issues_found) > 0,
                "recommended_fix": "移除sum()，直接使用len()计算列表长度"
            }
            
            if issues_found:
                print(f"❌ Bug5确认: 发现{len(issues_found)}处统计计算错误")
            else:
                print("✅ Bug5: 未发现统计计算错误")
                
        except Exception as e:
            self.results["critical_bugs"]["bug5"] = {
                "status": "DIAGNOSIS_ERROR",
                "severity": "CRITICAL",
                "error": str(e)
            }
    
    def _diagnose_bug6_websocket_null_pointer(self):
        """诊断Bug6: WebSocket连接空指针异常"""
        print("\n🔍 诊断Bug6: WebSocket连接空指针异常...")
        
        try:
            websocket_files = [
                "websocket/ws_client.py",
                "websocket/okx_ws.py", 
                "websocket/gate_ws.py",
                "websocket/bybit_ws.py"
            ]
            
            null_pointer_risks = []
            
            for file_path in websocket_files:
                full_path = self.project_root / file_path
                if not full_path.exists():
                    continue
                    
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                
                # 查找潜在的空指针风险
                for i, line in enumerate(lines, 1):
                    line_stripped = line.strip()
                    
                    # 检查直接调用self.ws.close()而无空指针检查
                    if (re.search(r'await\s+self\.ws\.close\(\)', line) and 
                        not self._has_null_check_before(lines, i-1)):
                        
                        null_pointer_risks.append({
                            "file": file_path,
                            "line_number": i,
                            "code": line_stripped,
                            "risk_type": "DIRECT_CLOSE_WITHOUT_CHECK",
                            "severity": "HIGH"
                        })
                    
                    # 检查其他可能的空指针调用
                    if re.search(r'self\.ws\.\w+\(\)', line) and 'if' not in line:
                        if not self._has_null_check_before(lines, i-1):
                            null_pointer_risks.append({
                                "file": file_path,
                                "line_number": i,
                                "code": line_stripped,
                                "risk_type": "POTENTIAL_NULL_ACCESS",
                                "severity": "MEDIUM"
                            })
            
            self.results["critical_bugs"]["bug6"] = {
                "status": "CONFIRMED" if null_pointer_risks else "NOT_FOUND",
                "severity": "CRITICAL",
                "risks_found": null_pointer_risks,
                "fix_required": len(null_pointer_risks) > 0,
                "recommended_fix": "在调用ws方法前添加if self.ws is not None检查"
            }
            
            print(f"❌ Bug6确认: 发现{len(null_pointer_risks)}处空指针风险")
                
        except Exception as e:
            self.results["critical_bugs"]["bug6"] = {
                "status": "DIAGNOSIS_ERROR",
                "severity": "CRITICAL",
                "error": str(e)
            }
    
    def _has_null_check_before(self, lines: List[str], line_index: int) -> bool:
        """检查前面几行是否有空指针检查"""
        # 检查前面5行内是否有空指针检查
        start_idx = max(0, line_index - 5)
        for i in range(start_idx, line_index):
            if i < len(lines):
                line = lines[i].strip()
                if 'if' in line and ('self.ws' in line) and ('None' in line or 'not' in line):
                    return True
        return False
    
    def _diagnose_bug7_timestamp_sync(self):
        """诊断Bug7: 时间戳同步状态不一致"""
        print("\n🔍 诊断Bug7: 时间戳同步状态不一致...")
        
        try:
            timestamp_processor_path = self.project_root / "websocket" / "unified_timestamp_processor.py"
            
            if not timestamp_processor_path.exists():
                self.results["critical_bugs"]["bug7"] = {
                    "status": "FILE_NOT_FOUND",
                    "severity": "CRITICAL"
                }
                return
            
            with open(timestamp_processor_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查同步状态管理相关代码
            sync_issues = []
            
            # 查找同步状态更新逻辑
            if 'sync_status' in content:
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if 'sync_status' in line and ('not_synced' in line or 'synced' in line):
                        sync_issues.append({
                            "line_number": i,
                            "code": line.strip(),
                            "context": "同步状态设置"
                        })
            
            self.results["critical_bugs"]["bug7"] = {
                "status": "REQUIRES_RUNTIME_CHECK",
                "severity": "HIGH",
                "file_path": str(timestamp_processor_path),
                "sync_issues": sync_issues,
                "note": "需要运行时检查同步状态逻辑"
            }
            
            print(f"⚠️ Bug7: 需要运行时验证，发现{len(sync_issues)}处同步状态相关代码")
                
        except Exception as e:
            self.results["critical_bugs"]["bug7"] = {
                "status": "DIAGNOSIS_ERROR",
                "severity": "HIGH", 
                "error": str(e)
            }
    
    def _diagnose_api_config_inconsistency(self):
        """诊断API调用优化器配置不一致问题"""
        print("\n🔍 诊断问题7: API调用优化器配置不一致...")
        
        try:
            # 检查API调用优化器配置
            api_optimizer_path = self.project_root / "core" / "api_call_optimizer.py"
            okx_exchange_path = self.project_root / "exchanges" / "okx_exchange.py"
            
            configs = {}
            
            # 读取API调用优化器配置
            if api_optimizer_path.exists():
                with open(api_optimizer_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找OKX限制配置
                okx_limit_match = re.search(r'"okx":\s*(\d+)', content)
                if okx_limit_match:
                    configs["api_optimizer_okx"] = int(okx_limit_match.group(1))
            
            # 读取OKX交易所配置
            if okx_exchange_path.exists():
                with open(okx_exchange_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找rate_limit设置
                rate_limit_match = re.search(r'self\.rate_limit\s*=\s*(\d+)', content)
                if rate_limit_match:
                    configs["okx_exchange_rate_limit"] = int(rate_limit_match.group(1))
            
            # 检查是否存在不一致
            inconsistency_found = False
            if ("api_optimizer_okx" in configs and 
                "okx_exchange_rate_limit" in configs and
                configs["api_optimizer_okx"] != configs["okx_exchange_rate_limit"]):
                inconsistency_found = True
            
            self.results["system_issues"]["api_config_inconsistency"] = {
                "status": "CONFIRMED" if inconsistency_found else "CONSISTENT",
                "severity": "HIGH" if inconsistency_found else "INFO",
                "configs_found": configs,
                "inconsistency": inconsistency_found,
                "details": {
                    "api_optimizer": configs.get("api_optimizer_okx", "NOT_FOUND"),
                    "okx_exchange": configs.get("okx_exchange_rate_limit", "NOT_FOUND")
                }
            }
            
            if inconsistency_found:
                print(f"❌ 配置不一致确认: API优化器={configs['api_optimizer_okx']}次/秒, OKX交易所={configs['okx_exchange_rate_limit']}次/秒")
            else:
                print("✅ API配置一致")
                
        except Exception as e:
            self.results["system_issues"]["api_config_inconsistency"] = {
                "status": "DIAGNOSIS_ERROR",
                "severity": "HIGH",
                "error": str(e)
            }
    
    def _diagnose_contract_info_failures(self):
        """诊断合约信息获取失败问题"""
        print("\n🔍 诊断问题8: 合约信息获取失败...")
        
        try:
            # 检查logs中的合约信息获取失败记录
            logs_dir = self.project_root / "logs"
            contract_failures = []
            
            if logs_dir.exists():
                for log_file in logs_dir.glob("*.log"):
                    try:
                        with open(log_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # 查找合约信息获取失败
                        failure_pattern = r'保证金接口:\s*(\w+)_([A-Z-]+)\s*\|\s*获取合约信息失败'
                        matches = re.findall(failure_pattern, content)
                        
                        for exchange, symbol in matches:
                            contract_failures.append({
                                "exchange": exchange,
                                "symbol": symbol,
                                "log_file": log_file.name
                            })
                    except:
                        continue
            
            # 去重
            unique_failures = []
            seen = set()
            for failure in contract_failures:
                key = (failure["exchange"], failure["symbol"])
                if key not in seen:
                    seen.add(key)
                    unique_failures.append(failure)
            
            self.results["system_issues"]["contract_info_failures"] = {
                "status": "CONFIRMED" if unique_failures else "NOT_FOUND",
                "severity": "HIGH" if unique_failures else "INFO",
                "failures_found": unique_failures,
                "total_failures": len(unique_failures),
                "affected_pairs": [f"{f['exchange']}_{f['symbol']}" for f in unique_failures]
            }
            
            if unique_failures:
                print(f"❌ 合约信息获取失败确认: {len(unique_failures)}个交易对")
                for failure in unique_failures:
                    print(f"   - {failure['exchange']}_{failure['symbol']}")
            else:
                print("✅ 未发现合约信息获取失败")
                
        except Exception as e:
            self.results["system_issues"]["contract_info_failures"] = {
                "status": "DIAGNOSIS_ERROR",
                "severity": "HIGH",
                "error": str(e)
            }
    
    def _diagnose_trading_pair_validation(self):
        """诊断交易对配置验证缺失问题"""
        print("\n🔍 诊断问题9: 交易对配置验证缺失...")
        
        try:
            # 检查.env文件中的TARGET_SYMBOLS配置
            env_files = [
                self.project_root / ".env",
                self.project_root / "123" / ".env"
            ]
            
            target_symbols = []
            
            for env_file in env_files:
                if env_file.exists():
                    with open(env_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    symbols_match = re.search(r'TARGET_SYMBOLS\s*=\s*(.+)', content)
                    if symbols_match:
                        symbols_str = symbols_match.group(1).strip()
                        target_symbols = [s.strip() for s in symbols_str.split(',')]
                        break
            
            self.results["system_issues"]["trading_pair_validation"] = {
                "status": "SYMBOLS_FOUND" if target_symbols else "NO_SYMBOLS_FOUND",
                "severity": "MEDIUM",
                "target_symbols": target_symbols,
                "total_symbols": len(target_symbols),
                "validation_needed": True,
                "note": "需要验证这些交易对在各交易所的支持情况"
            }
            
            if target_symbols:
                print(f"⚠️ 发现{len(target_symbols)}个配置的交易对，需要验证支持情况")
            else:
                print("❌ 未找到TARGET_SYMBOLS配置")
                
        except Exception as e:
            self.results["system_issues"]["trading_pair_validation"] = {
                "status": "DIAGNOSIS_ERROR",
                "severity": "MEDIUM",
                "error": str(e)
            }
    
    def _diagnose_websocket_connection_issues(self):
        """诊断WebSocket连接问题"""
        print("\n🔍 诊断WebSocket连接问题...")
        
        try:
            # 检查logs中的WebSocket错误
            logs_dir = self.project_root / "logs"
            websocket_issues = {
                "http_503_errors": [],
                "too_many_requests": [],
                "null_pointer_errors": []
            }
            
            if logs_dir.exists():
                for log_file in logs_dir.glob("*.log"):
                    try:
                        with open(log_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # HTTP 503错误
                        if "HTTP 503" in content:
                            websocket_issues["http_503_errors"].append(log_file.name)
                        
                        # Too Many Requests错误
                        if "Too Many Requests" in content and "50011" in content:
                            websocket_issues["too_many_requests"].append(log_file.name)
                        
                        # 空指针错误
                        if "'NoneType' object has no attribute 'close'" in content:
                            websocket_issues["null_pointer_errors"].append(log_file.name)
                            
                    except:
                        continue
            
            total_issues = sum(len(v) for v in websocket_issues.values())
            
            self.results["system_issues"]["websocket_connection"] = {
                "status": "ISSUES_FOUND" if total_issues > 0 else "NO_ISSUES",
                "severity": "HIGH" if total_issues > 0 else "INFO",
                "issues_summary": websocket_issues,
                "total_issue_files": total_issues
            }
            
            if total_issues > 0:
                print(f"❌ WebSocket连接问题确认: 在{total_issues}个日志文件中发现问题")
            else:
                print("✅ 未发现WebSocket连接问题")
                
        except Exception as e:
            self.results["system_issues"]["websocket_connection"] = {
                "status": "DIAGNOSIS_ERROR",
                "severity": "HIGH",
                "error": str(e)
            }
    
    def _generate_diagnosis_summary(self):
        """生成诊断总结"""
        
        # 统计问题
        critical_count = sum(1 for bug in self.results["critical_bugs"].values() 
                           if bug.get("status") == "CONFIRMED")
        
        system_issues_count = sum(1 for issue in self.results["system_issues"].values()
                                if issue.get("status") in ["CONFIRMED", "ISSUES_FOUND"])
        
        # 生成修复建议
        recommendations = []
        
        if self.results["critical_bugs"].get("bug5", {}).get("fix_required"):
            recommendations.append("🔥 紧急: 修复错误处理器统计计算逻辑，移除错误的sum(len())调用")
        
        if self.results["critical_bugs"].get("bug6", {}).get("fix_required"):
            recommendations.append("🔥 紧急: 添加WebSocket连接空指针检查，防止NoneType错误")
        
        if self.results["system_issues"].get("api_config_inconsistency", {}).get("inconsistency"):
            recommendations.append("⚠️ 高优先级: 统一API限速配置，确保调用优化器和交易所设置一致")
        
        if self.results["system_issues"].get("contract_info_failures", {}).get("failures_found"):
            recommendations.append("⚠️ 高优先级: 修复合约信息获取失败问题，实施重试机制")
        
        if self.results["system_issues"].get("websocket_connection", {}).get("total_issue_files", 0) > 0:
            recommendations.append("⚠️ 高优先级: 解决WebSocket连接问题，优化API限流机制")
        
        self.results["diagnosis_summary"] = {
            "total_critical_bugs": critical_count,
            "total_system_issues": system_issues_count,
            "overall_status": "CRITICAL" if critical_count > 0 else ("ISSUES_FOUND" if system_issues_count > 0 else "HEALTHY"),
            "diagnosis_completion": "100%",
            "next_steps": "根据修复建议进行手动修复"
        }
        
        self.results["recommendations"] = recommendations
        
        # 打印总结
        print(f"\n🎯 诊断总结:")
        print(f"   - 关键Bug: {critical_count}个")
        print(f"   - 系统问题: {system_issues_count}个")
        print(f"   - 整体状态: {self.results['diagnosis_summary']['overall_status']}")
        print(f"   - 修复建议: {len(recommendations)}条")

def main():
    """主函数"""
    print("🔍 综合关键问题诊断脚本启动")
    print("=" * 60)
    
    try:
        # 创建诊断引擎
        engine = CriticalIssuesDiagnosisEngine()
        
        # 运行诊断
        results = engine.run_comprehensive_diagnosis()
        
        # 保存结果
        output_file = engine.project_root / "diagnostic_results" / f"critical_issues_diagnosis_{int(time.time())}.json"
        output_file.parent.mkdir(exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ 诊断完成，结果已保存到: {output_file}")
        print("🔍 诊断脚本执行成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断脚本执行失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)