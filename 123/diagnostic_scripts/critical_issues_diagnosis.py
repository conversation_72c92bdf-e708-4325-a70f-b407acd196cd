#!/usr/bin/env python3
"""
🔥 关键问题精确诊断脚本
确保差价精准性、三交易所一致性、高速性能的前提下进行诊断
"""

import os
import sys
import re
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CriticalIssuesDiagnostic:
    """🔥 关键问题诊断器 - 确保三交易所一致性"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.results = {
            "timestamp": time.time(),
            "critical_bugs": {},
            "system_problems": {},
            "architecture_issues": {},
            "consistency_check": {},
            "summary": {}
        }
        
    def run_full_diagnosis(self) -> Dict[str, Any]:
        """运行完整诊断"""
        logger.info("🔍 开始关键问题精确诊断...")
        
        # 1. Bug修复验证
        self._diagnose_bug5_error_handler_stats()
        self._diagnose_bug6_websocket_null_pointer()
        self._diagnose_bug7_timestamp_sync()
        
        # 2. 系统问题诊断
        self._diagnose_problem7_api_config_inconsistency()
        self._diagnose_problem8_contract_info_failures()
        self._diagnose_problem9_config_validation_missing()
        
        # 3. 架构重复问题
        self._diagnose_websocket_management_duplication()
        self._diagnose_api_rate_limit_duplication()
        
        # 4. 三交易所一致性检查
        self._check_three_exchange_consistency()
        
        # 5. 生成诊断报告
        self._generate_diagnosis_summary()
        
        return self.results
    
    def _diagnose_bug5_error_handler_stats(self):
        """诊断Bug5: 错误处理器统计计算错误"""
        logger.info("🔍 诊断Bug5: 错误处理器统计计算错误...")
        
        try:
            error_handler_path = self.project_root / "websocket" / "error_handler.py"
            if not error_handler_path.exists():
                self.results["critical_bugs"]["bug5"] = {
                    "status": "FILE_NOT_FOUND",
                    "severity": "CRITICAL",
                    "message": "error_handler.py文件不存在"
                }
                return
            
            with open(error_handler_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查错误的sum(len([...]))模式
            problematic_patterns = [
                r'sum\(len\(\[.*?\]\)\)',  # sum(len([...]))模式
                r'total_attempts\s*=\s*sum\(len\(',
                r'successful_recoveries\s*=\s*sum\(len\('
            ]
            
            issues_found = []
            lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                for pattern in problematic_patterns:
                    if re.search(pattern, line):
                        issues_found.append({
                            "line_number": i,
                            "line_content": line.strip(),
                            "pattern": pattern,
                            "issue": "sum(len([...])) 语法错误"
                        })
            
            if issues_found:
                self.results["critical_bugs"]["bug5"] = {
                    "status": "CONFIRMED",
                    "severity": "CRITICAL",
                    "issues_count": len(issues_found),
                    "issues": issues_found,
                    "fix_required": True,
                    "message": f"发现{len(issues_found)}处sum(len([...]))语法错误"
                }
            else:
                self.results["critical_bugs"]["bug5"] = {
                    "status": "FIXED",
                    "severity": "LOW",
                    "message": "未发现sum(len([...]))语法错误"
                }
                
        except Exception as e:
            self.results["critical_bugs"]["bug5"] = {
                "status": "ERROR",
                "severity": "HIGH",
                "message": f"诊断失败: {e}"
            }
    
    def _diagnose_bug6_websocket_null_pointer(self):
        """诊断Bug6: WebSocket连接空指针异常"""
        logger.info("🔍 诊断Bug6: WebSocket连接空指针异常...")
        
        websocket_files = [
            "websocket/ws_client.py",
            "websocket/okx_ws.py", 
            "websocket/gate_ws.py",
            "websocket/bybit_ws.py"
        ]
        
        null_pointer_risks = []
        fixed_patterns = []
        
        for file_path in websocket_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue
                
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                
                for i, line in enumerate(lines, 1):
                    line_stripped = line.strip()
                    
                    # 检查空指针风险模式
                    if re.search(r'await\s+self\.ws\.close\(\)', line_stripped):
                        # 检查前面是否有空指针检查
                        context_start = max(0, i-3)
                        context_lines = lines[context_start:i]
                        has_null_check = any('if self.ws is not None:' in ctx or 
                                           'if self.ws:' in ctx for ctx in context_lines)
                        
                        if not has_null_check:
                            null_pointer_risks.append({
                                "file": file_path,
                                "line_number": i,
                                "line_content": line_stripped,
                                "risk": "直接调用ws.close()无空指针检查"
                            })
                        else:
                            fixed_patterns.append({
                                "file": file_path,
                                "line_number": i,
                                "line_content": line_stripped,
                                "status": "已修复空指针检查"
                            })
                            
            except Exception as e:
                logger.error(f"检查文件{file_path}失败: {e}")
        
        self.results["critical_bugs"]["bug6"] = {
            "status": "CONFIRMED" if null_pointer_risks else "PARTIALLY_FIXED",
            "severity": "HIGH" if null_pointer_risks else "MEDIUM",
            "null_pointer_risks": null_pointer_risks,
            "fixed_patterns": fixed_patterns,
            "risks_count": len(null_pointer_risks),
            "fixed_count": len(fixed_patterns),
            "message": f"发现{len(null_pointer_risks)}处空指针风险，{len(fixed_patterns)}处已修复"
        }
    
    def _diagnose_bug7_timestamp_sync(self):
        """诊断Bug7: 时间戳同步状态不一致"""
        logger.info("🔍 诊断Bug7: 时间戳同步状态不一致...")
        
        try:
            timestamp_processor_path = self.project_root / "websocket" / "unified_timestamp_processor.py"
            if not timestamp_processor_path.exists():
                self.results["critical_bugs"]["bug7"] = {
                    "status": "FILE_NOT_FOUND",
                    "severity": "CRITICAL",
                    "message": "unified_timestamp_processor.py文件不存在"
                }
                return
            
            # 检查时间戳处理器实现
            with open(timestamp_processor_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键方法是否存在
            key_methods = [
                "get_synced_timestamp",
                "sync_exchange_time", 
                "get_sync_status",
                "initialize_all_timestamp_processors"
            ]
            
            method_status = {}
            for method in key_methods:
                if f"def {method}" in content:
                    method_status[method] = "EXISTS"
                else:
                    method_status[method] = "MISSING"
            
            missing_methods = [m for m, s in method_status.items() if s == "MISSING"]
            
            self.results["critical_bugs"]["bug7"] = {
                "status": "PARTIALLY_IMPLEMENTED" if missing_methods else "IMPLEMENTED",
                "severity": "MEDIUM" if missing_methods else "LOW",
                "method_status": method_status,
                "missing_methods": missing_methods,
                "message": f"时间戳处理器实现状态: {len(key_methods)-len(missing_methods)}/{len(key_methods)}个方法已实现"
            }
            
        except Exception as e:
            self.results["critical_bugs"]["bug7"] = {
                "status": "ERROR",
                "severity": "HIGH",
                "message": f"诊断失败: {e}"
            }

    def _diagnose_problem7_api_config_inconsistency(self):
        """诊断问题7: API调用优化器配置不一致"""
        logger.info("🔍 诊断问题7: API调用优化器配置不一致...")

        try:
            # 检查API调用优化器配置
            optimizer_path = self.project_root / "core" / "api_call_optimizer.py"
            okx_exchange_path = self.project_root / "exchanges" / "okx_exchange.py"

            optimizer_config = None
            okx_config = None

            if optimizer_path.exists():
                with open(optimizer_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 查找OKX限速配置
                    match = re.search(r'"okx":\s*(\d+)', content)
                    if match:
                        optimizer_config = int(match.group(1))

            if okx_exchange_path.exists():
                with open(okx_exchange_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 查找rate_limit配置
                    match = re.search(r'self\.rate_limit\s*=\s*(\d+)', content)
                    if match:
                        okx_config = int(match.group(1))

            if optimizer_config and okx_config:
                if optimizer_config == okx_config:
                    self.results["system_problems"]["problem7"] = {
                        "status": "CONSISTENT",
                        "severity": "LOW",
                        "optimizer_config": optimizer_config,
                        "okx_config": okx_config,
                        "message": f"API限速配置一致: {optimizer_config}次/秒"
                    }
                else:
                    self.results["system_problems"]["problem7"] = {
                        "status": "INCONSISTENT",
                        "severity": "HIGH",
                        "optimizer_config": optimizer_config,
                        "okx_config": okx_config,
                        "message": f"API限速配置不一致: 优化器{optimizer_config}次/秒 vs OKX{okx_config}次/秒"
                    }
            else:
                self.results["system_problems"]["problem7"] = {
                    "status": "CONFIG_NOT_FOUND",
                    "severity": "MEDIUM",
                    "optimizer_config": optimizer_config,
                    "okx_config": okx_config,
                    "message": "无法找到完整的API限速配置"
                }

        except Exception as e:
            self.results["system_problems"]["problem7"] = {
                "status": "ERROR",
                "severity": "HIGH",
                "message": f"诊断失败: {e}"
            }

    def _diagnose_problem8_contract_info_failures(self):
        """诊断问题8: 合约信息获取失败导致保证金计算错误"""
        logger.info("🔍 诊断问题8: 合约信息获取失败...")

        # 检查日志文件中的合约信息获取失败记录
        log_files = list(self.project_root.glob("logs/*.log"))
        contract_failures = []

        for log_file in log_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 查找合约信息获取失败的模式
                failure_patterns = [
                    r'获取合约信息失败.*?([A-Z]+-USDT)',
                    r'合约信息获取.*?失败.*?([A-Z]+-USDT)',
                    r'contract.*?info.*?failed.*?([A-Z]+-USDT)'
                ]

                for pattern in failure_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        contract_failures.append({
                            "symbol": match,
                            "log_file": log_file.name,
                            "pattern": pattern
                        })

            except Exception as e:
                logger.warning(f"读取日志文件{log_file}失败: {e}")

        # 统计失败的交易对
        failed_symbols = list(set([f["symbol"] for f in contract_failures]))

        self.results["system_problems"]["problem8"] = {
            "status": "CONFIRMED" if contract_failures else "NO_FAILURES_FOUND",
            "severity": "HIGH" if len(failed_symbols) > 5 else "MEDIUM",
            "total_failures": len(contract_failures),
            "failed_symbols": failed_symbols,
            "failure_details": contract_failures[:10],  # 只保留前10个详情
            "message": f"发现{len(contract_failures)}次合约信息获取失败，涉及{len(failed_symbols)}个交易对"
        }

    def _diagnose_problem9_config_validation_missing(self):
        """诊断问题9: 交易对配置验证缺失"""
        logger.info("🔍 诊断问题9: 交易对配置验证缺失...")

        try:
            # 检查是否存在配置验证工具
            validation_tools = []

            # 查找可能的配置验证文件
            potential_files = [
                "core/config_validator.py",
                "utils/config_validator.py",
                "config/validator.py",
                "core/trading_system_initializer.py"
            ]

            for file_path in potential_files:
                full_path = self.project_root / file_path
                if full_path.exists():
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 检查是否包含配置验证逻辑
                    validation_patterns = [
                        r'def.*?validate.*?config',
                        r'def.*?check.*?symbols',
                        r'def.*?verify.*?trading.*?pairs',
                        r'TARGET_SYMBOLS.*?validation'
                    ]

                    found_patterns = []
                    for pattern in validation_patterns:
                        if re.search(pattern, content, re.IGNORECASE):
                            found_patterns.append(pattern)

                    if found_patterns:
                        validation_tools.append({
                            "file": file_path,
                            "patterns_found": found_patterns,
                            "has_validation": True
                        })

            if validation_tools:
                self.results["system_problems"]["problem9"] = {
                    "status": "PARTIALLY_IMPLEMENTED",
                    "severity": "MEDIUM",
                    "validation_tools": validation_tools,
                    "tools_count": len(validation_tools),
                    "message": f"发现{len(validation_tools)}个配置验证工具"
                }
            else:
                self.results["system_problems"]["problem9"] = {
                    "status": "MISSING",
                    "severity": "HIGH",
                    "validation_tools": [],
                    "tools_count": 0,
                    "message": "未发现配置验证工具，建议创建启动时配置验证机制"
                }

        except Exception as e:
            self.results["system_problems"]["problem9"] = {
                "status": "ERROR",
                "severity": "HIGH",
                "message": f"诊断失败: {e}"
            }

    def _diagnose_websocket_management_duplication(self):
        """诊断WebSocket管理模块重复问题"""
        logger.info("🔍 诊断WebSocket管理模块重复...")

        websocket_managers = [
            {
                "file": "websocket/ws_manager.py",
                "class": "WebSocketManager",
                "responsibilities": ["连接管理", "数据聚合", "状态监控"]
            },
            {
                "file": "websocket/unified_connection_pool_manager.py",
                "class": "UnifiedConnectionPoolManager",
                "responsibilities": ["连接池管理", "重连策略", "故障转移"]
            },
            {
                "file": "websocket/unified_websocket_pool_manager.py",
                "class": "UnifiedWebSocketPoolManager",
                "responsibilities": ["WebSocket池管理", "客户端注册", "健康检查"]
            }
        ]

        existing_managers = []
        overlapping_responsibilities = []

        for manager in websocket_managers:
            full_path = self.project_root / manager["file"]
            if full_path.exists():
                existing_managers.append(manager)

                # 检查职责重叠
                for other in websocket_managers:
                    if other != manager and (self.project_root / other["file"]).exists():
                        common_responsibilities = set(manager["responsibilities"]) & set(other["responsibilities"])
                        if common_responsibilities:
                            overlapping_responsibilities.append({
                                "manager1": manager["class"],
                                "manager2": other["class"],
                                "overlapping": list(common_responsibilities)
                            })

        self.results["architecture_issues"]["websocket_duplication"] = {
            "status": "CONFIRMED" if len(existing_managers) > 1 else "NO_DUPLICATION",
            "severity": "HIGH" if len(existing_managers) > 2 else "MEDIUM",
            "existing_managers": existing_managers,
            "managers_count": len(existing_managers),
            "overlapping_responsibilities": overlapping_responsibilities,
            "message": f"发现{len(existing_managers)}个WebSocket管理模块，存在职责重叠"
        }

    def _diagnose_api_rate_limit_duplication(self):
        """诊断API限速重复实现"""
        logger.info("🔍 诊断API限速重复实现...")

        rate_limit_implementations = []

        # 检查可能包含限速逻辑的文件
        files_to_check = [
            "core/api_call_optimizer.py",
            "exchanges/okx_exchange.py",
            "exchanges/gate_exchange.py",
            "exchanges/bybit_exchange.py",
            "exchanges/exchanges_base.py"
        ]

        for file_path in files_to_check:
            full_path = self.project_root / file_path
            if not full_path.exists():
                continue

            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 查找限速相关的实现
                rate_limit_patterns = [
                    r'rate_limit\s*=',
                    r'def.*?rate.*?limit',
                    r'class.*?RateLimit',
                    r'asyncio\.sleep.*?rate',
                    r'time\.sleep.*?limit'
                ]

                found_patterns = []
                for pattern in rate_limit_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        found_patterns.extend(matches)

                if found_patterns:
                    rate_limit_implementations.append({
                        "file": file_path,
                        "patterns_found": found_patterns,
                        "implementation_count": len(found_patterns)
                    })

            except Exception as e:
                logger.warning(f"检查文件{file_path}失败: {e}")

        self.results["architecture_issues"]["api_rate_limit_duplication"] = {
            "status": "CONFIRMED" if len(rate_limit_implementations) > 1 else "CENTRALIZED",
            "severity": "MEDIUM" if len(rate_limit_implementations) > 2 else "LOW",
            "implementations": rate_limit_implementations,
            "implementations_count": len(rate_limit_implementations),
            "message": f"发现{len(rate_limit_implementations)}个文件包含限速实现"
        }

    def _check_three_exchange_consistency(self):
        """检查三交易所一致性"""
        logger.info("🔍 检查三交易所一致性...")

        exchanges = ["okx", "gate", "bybit"]
        consistency_results = {}

        # 检查WebSocket客户端一致性
        websocket_consistency = self._check_websocket_client_consistency(exchanges)
        consistency_results["websocket_clients"] = websocket_consistency

        # 检查交易所类一致性
        exchange_consistency = self._check_exchange_class_consistency(exchanges)
        consistency_results["exchange_classes"] = exchange_consistency

        # 检查错误处理一致性
        error_handling_consistency = self._check_error_handling_consistency(exchanges)
        consistency_results["error_handling"] = error_handling_consistency

        # 计算总体一致性分数
        total_checks = sum(len(result.get("checks", {})) for result in consistency_results.values())
        consistent_checks = sum(
            sum(1 for check in result.get("checks", {}).values() if check.get("consistent", False))
            for result in consistency_results.values()
        )

        consistency_score = (consistent_checks / total_checks * 100) if total_checks > 0 else 0

        self.results["consistency_check"] = {
            "overall_score": consistency_score,
            "total_checks": total_checks,
            "consistent_checks": consistent_checks,
            "details": consistency_results,
            "status": "GOOD" if consistency_score >= 80 else "NEEDS_IMPROVEMENT" if consistency_score >= 60 else "POOR"
        }

    def _check_websocket_client_consistency(self, exchanges: List[str]) -> Dict[str, Any]:
        """检查WebSocket客户端一致性"""
        websocket_files = {
            "okx": "websocket/okx_ws.py",
            "gate": "websocket/gate_ws.py",
            "bybit": "websocket/bybit_ws.py"
        }

        consistency_checks = {}

        # 检查基类继承
        base_class_check = {}
        for exchange in exchanges:
            file_path = self.project_root / websocket_files[exchange]
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                base_class_check[exchange] = "WebSocketClient" in content

        consistency_checks["base_class_inheritance"] = {
            "consistent": len(set(base_class_check.values())) == 1,
            "details": base_class_check
        }

        # 检查关键方法存在性
        key_methods = ["run", "close", "send_heartbeat", "handle_message"]
        method_checks = {}

        for method in key_methods:
            method_existence = {}
            for exchange in exchanges:
                file_path = self.project_root / websocket_files[exchange]
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    method_existence[exchange] = f"def {method}" in content

            method_checks[method] = {
                "consistent": len(set(method_existence.values())) == 1 and all(method_existence.values()),
                "details": method_existence
            }

        consistency_checks.update(method_checks)

        return {
            "checks": consistency_checks,
            "summary": f"WebSocket客户端一致性检查完成"
        }

    def _check_exchange_class_consistency(self, exchanges: List[str]) -> Dict[str, Any]:
        """检查交易所类一致性"""
        exchange_files = {
            "okx": "exchanges/okx_exchange.py",
            "gate": "exchanges/gate_exchange.py",
            "bybit": "exchanges/bybit_exchange.py"
        }

        consistency_checks = {}

        # 检查基类继承
        base_class_check = {}
        for exchange in exchanges:
            file_path = self.project_root / exchange_files[exchange]
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                base_class_check[exchange] = "ExchangeBase" in content or "BaseExchange" in content

        consistency_checks["base_class_inheritance"] = {
            "consistent": len(set(base_class_check.values())) == 1,
            "details": base_class_check
        }

        return {
            "checks": consistency_checks,
            "summary": f"交易所类一致性检查完成"
        }

    def _check_error_handling_consistency(self, exchanges: List[str]) -> Dict[str, Any]:
        """检查错误处理一致性"""
        consistency_checks = {}

        # 检查是否都使用统一错误处理器
        error_handler_usage = {}
        websocket_files = {
            "okx": "websocket/okx_ws.py",
            "gate": "websocket/gate_ws.py",
            "bybit": "websocket/bybit_ws.py"
        }

        for exchange in exchanges:
            file_path = self.project_root / websocket_files[exchange]
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                error_handler_usage[exchange] = "get_unified_error_handler" in content or "UnifiedErrorHandler" in content

        consistency_checks["unified_error_handler"] = {
            "consistent": len(set(error_handler_usage.values())) == 1 and all(error_handler_usage.values()),
            "details": error_handler_usage
        }

        return {
            "checks": consistency_checks,
            "summary": f"错误处理一致性检查完成"
        }

    def _generate_diagnosis_summary(self):
        """生成诊断总结"""
        logger.info("📊 生成诊断总结...")

        # 统计各类问题
        critical_bugs = len([bug for bug in self.results["critical_bugs"].values()
                           if bug.get("status") == "CONFIRMED"])
        system_problems = len([prob for prob in self.results["system_problems"].values()
                             if prob.get("status") in ["CONFIRMED", "INCONSISTENT", "MISSING"]])
        architecture_issues = len([issue for issue in self.results["architecture_issues"].values()
                                 if issue.get("status") == "CONFIRMED"])

        # 计算总体健康分数
        total_issues = critical_bugs + system_problems + architecture_issues
        consistency_score = self.results.get("consistency_check", {}).get("overall_score", 0)

        if total_issues == 0 and consistency_score >= 90:
            health_status = "EXCELLENT"
        elif total_issues <= 2 and consistency_score >= 80:
            health_status = "GOOD"
        elif total_issues <= 5 and consistency_score >= 60:
            health_status = "FAIR"
        else:
            health_status = "POOR"

        self.results["summary"] = {
            "health_status": health_status,
            "total_issues": total_issues,
            "critical_bugs": critical_bugs,
            "system_problems": system_problems,
            "architecture_issues": architecture_issues,
            "consistency_score": consistency_score,
            "recommendations": self._generate_recommendations()
        }

        logger.info(f"✅ 诊断完成 - 健康状态: {health_status}, 总问题数: {total_issues}")

    def _generate_recommendations(self) -> List[str]:
        """生成修复建议"""
        recommendations = []

        # 基于诊断结果生成建议
        if self.results["critical_bugs"].get("bug5", {}).get("status") == "CONFIRMED":
            recommendations.append("立即修复错误处理器统计计算错误(Bug5)")

        if self.results["critical_bugs"].get("bug6", {}).get("risks_count", 0) > 0:
            recommendations.append("添加WebSocket连接空指针检查(Bug6)")

        if self.results["system_problems"].get("problem7", {}).get("status") == "INCONSISTENT":
            recommendations.append("统一API限速配置，消除不一致性(问题7)")

        if self.results["architecture_issues"].get("websocket_duplication", {}).get("managers_count", 0) > 1:
            recommendations.append("整合重复的WebSocket管理模块，消除职责重叠")

        if self.results["consistency_check"].get("overall_score", 0) < 80:
            recommendations.append("提高三交易所一致性，统一接口和错误处理逻辑")

        return recommendations


def main():
    """主函数"""
    print("🔥 关键问题精确诊断脚本")
    print("=" * 50)

    # 创建诊断器
    diagnostic = CriticalIssuesDiagnostic()

    # 运行诊断
    results = diagnostic.run_full_diagnosis()

    # 输出结果
    print("\n📊 诊断结果:")
    print(f"健康状态: {results['summary']['health_status']}")
    print(f"总问题数: {results['summary']['total_issues']}")
    print(f"一致性分数: {results['summary']['consistency_score']:.1f}%")

    print("\n🔧 修复建议:")
    for i, rec in enumerate(results['summary']['recommendations'], 1):
        print(f"{i}. {rec}")

    # 保存详细结果
    output_file = Path(__file__).parent / f"diagnosis_results_{int(time.time())}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"\n📄 详细结果已保存到: {output_file}")

    return results


if __name__ == "__main__":
    main()
