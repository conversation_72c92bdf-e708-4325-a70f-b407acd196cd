#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 修复效果验证脚本
验证Bug5、Bug6、问题7的修复效果

作者: <PERSON> Code Assistant
日期: 2025-08-03
版本: v1.0 - 修复验证版
"""

import sys
import os
import json
import re
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class FixVerificationEngine:
    """修复验证引擎"""
    
    def __init__(self):
        self.project_root = project_root
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "verification_summary": {},
            "bug5_fix": {},
            "bug6_fix": {},
            "problem7_fix": {},
            "overall_status": "UNKNOWN"
        }
        
    def run_verification(self) -> Dict[str, Any]:
        """运行修复验证"""
        print("🔍 开始修复效果验证...")
        
        # 验证各项修复
        self._verify_bug5_fix()
        self._verify_bug6_fix()
        self._verify_problem7_fix()
        
        # 生成总结
        self._generate_verification_summary()
        
        return self.results
    
    def _verify_bug5_fix(self):
        """验证Bug5修复效果"""
        print("\n🔍 验证Bug5修复: 错误处理器统计计算...")
        
        try:
            error_handler_path = self.project_root / "websocket" / "error_handler.py"
            
            if not error_handler_path.exists():
                self.results["bug5_fix"] = {
                    "status": "FILE_NOT_FOUND",
                    "verified": False
                }
                return
            
            with open(error_handler_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # 检查是否还有错误的sum(len())模式
            bad_patterns_found = []
            good_patterns_found = []
            
            for i, line in enumerate(lines, 1):
                # 查找错误模式
                if re.search(r'sum\(len\(\[.*?\]\)\)', line):
                    bad_patterns_found.append({
                        "line_number": i,
                        "code": line.strip()
                    })
                
                # 查找正确的修复模式
                if (re.search(r'total_attempts\s*=\s*len\(\[', line) or 
                    re.search(r'successful_recoveries\s*=\s*len\(\[', line)):
                    good_patterns_found.append({
                        "line_number": i,
                        "code": line.strip()
                    })
            
            fix_verified = len(bad_patterns_found) == 0 and len(good_patterns_found) >= 2
            
            self.results["bug5_fix"] = {
                "status": "VERIFIED" if fix_verified else "ISSUES_REMAIN",
                "verified": fix_verified,
                "bad_patterns_found": bad_patterns_found,
                "good_patterns_found": good_patterns_found,
                "details": f"发现{len(good_patterns_found)}处正确修复，{len(bad_patterns_found)}处残留问题"
            }
            
            if fix_verified:
                print("✅ Bug5修复验证通过: 统计计算错误已修复")
            else:
                print(f"❌ Bug5修复验证失败: 仍有{len(bad_patterns_found)}处问题")
                
        except Exception as e:
            self.results["bug5_fix"] = {
                "status": "VERIFICATION_ERROR",
                "verified": False,
                "error": str(e)
            }
    
    def _verify_bug6_fix(self):
        """验证Bug6修复效果"""
        print("\n🔍 验证Bug6修复: WebSocket空指针检查...")
        
        try:
            websocket_files = [
                "websocket/ws_client.py",
                "websocket/okx_ws.py",
                "websocket/gate_ws.py",
                "websocket/bybit_ws.py"
            ]
            
            total_fixes = 0
            files_checked = 0
            
            for file_path in websocket_files:
                full_path = self.project_root / file_path
                if not full_path.exists():
                    continue
                    
                files_checked += 1
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                
                # 查找修复的空指针检查模式
                for i, line in enumerate(lines, 1):
                    if i > 1:  # 检查前一行
                        prev_line = lines[i-2].strip()
                        current_line = line.strip()
                        
                        # 检查是否有"if self.ws is not None:" 后跟 "await self.ws.close()"
                        if ('if self.ws is not None:' in prev_line and 
                            'await self.ws.close()' in current_line):
                            total_fixes += 1
            
            # 检查ws_client.py中的特定修复位置
            ws_client_path = self.project_root / "websocket" / "ws_client.py"
            specific_fixes_found = 0
            
            if ws_client_path.exists():
                with open(ws_client_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查第326行和第331行附近的修复
                if 'if self.ws is not None:' in content and 'await self.ws.close()' in content:
                    # 更精确的检查
                    lines = content.split('\n')
                    for i in range(len(lines) - 1):
                        if ('频道订阅失败' in lines[i] and 
                            'if self.ws is not None:' in lines[i+1]):
                            specific_fixes_found += 1
                        elif ('频道订阅超时' in lines[i] and 
                              'if self.ws is not None:' in lines[i+1]):
                            specific_fixes_found += 1
            
            fix_verified = specific_fixes_found >= 2  # 至少修复了两个关键位置
            
            self.results["bug6_fix"] = {
                "status": "VERIFIED" if fix_verified else "PARTIAL_FIX",
                "verified": fix_verified,
                "total_null_checks_added": total_fixes,
                "specific_fixes_found": specific_fixes_found,
                "files_checked": files_checked,
                "details": f"在{files_checked}个文件中发现{total_fixes}处空指针检查，{specific_fixes_found}处关键修复"
            }
            
            if fix_verified:
                print(f"✅ Bug6修复验证通过: 发现{specific_fixes_found}处关键空指针修复")
            else:
                print(f"⚠️ Bug6修复验证部分通过: 发现{total_fixes}处修复，需要更多检查")
                
        except Exception as e:
            self.results["bug6_fix"] = {
                "status": "VERIFICATION_ERROR",
                "verified": False,
                "error": str(e)
            }
    
    def _verify_problem7_fix(self):
        """验证问题7修复效果"""
        print("\n🔍 验证问题7修复: API配置一致性...")
        
        try:
            # 检查API调用优化器配置
            api_optimizer_path = self.project_root / "core" / "api_call_optimizer.py"
            okx_exchange_path = self.project_root / "exchanges" / "okx_exchange.py"
            
            api_optimizer_config = None
            okx_exchange_config = None
            unified_config_used = False
            
            # 读取API调用优化器配置
            if api_optimizer_path.exists():
                with open(api_optimizer_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                okx_limit_match = re.search(r'"okx":\s*(\d+)', content)
                if okx_limit_match:
                    api_optimizer_config = int(okx_limit_match.group(1))
            
            # 检查OKX交易所是否使用统一配置
            if okx_exchange_path.exists():
                with open(okx_exchange_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有统一配置的导入和使用
                if ('from core.api_call_optimizer import APICallOptimizer' in content and
                    'optimizer.rate_limits.get("okx"' in content):
                    unified_config_used = True
                
                # 检查是否移除了硬编码配置
                hardcoded_limit_match = re.search(r'self\.rate_limit\s*=\s*(\d+)(?!\s*#.*统一配置)', content)
                if hardcoded_limit_match and not unified_config_used:
                    okx_exchange_config = int(hardcoded_limit_match.group(1))
            
            # 验证修复效果
            fix_verified = unified_config_used and api_optimizer_config is not None
            
            self.results["problem7_fix"] = {
                "status": "VERIFIED" if fix_verified else "NOT_VERIFIED",
                "verified": fix_verified,
                "api_optimizer_config": api_optimizer_config,
                "unified_config_used": unified_config_used,
                "configuration_source": "统一配置" if unified_config_used else "硬编码配置",
                "details": f"API优化器配置: {api_optimizer_config}次/秒, 统一配置使用: {unified_config_used}"
            }
            
            if fix_verified:
                print(f"✅ 问题7修复验证通过: OKX交易所使用统一配置{api_optimizer_config}次/秒")
            else:
                print("❌ 问题7修复验证失败: 仍使用硬编码配置或配置不一致")
                
        except Exception as e:
            self.results["problem7_fix"] = {
                "status": "VERIFICATION_ERROR",
                "verified": False,
                "error": str(e)
            }
    
    def _generate_verification_summary(self):
        """生成验证总结"""
        verified_count = sum(1 for fix in [
            self.results["bug5_fix"],
            self.results["bug6_fix"], 
            self.results["problem7_fix"]
        ] if fix.get("verified", False))
        
        total_fixes = 3
        success_rate = (verified_count / total_fixes) * 100
        
        if success_rate == 100:
            overall_status = "ALL_FIXES_VERIFIED"
        elif success_rate >= 66:
            overall_status = "MOST_FIXES_VERIFIED"
        elif success_rate >= 33:
            overall_status = "SOME_FIXES_VERIFIED"
        else:
            overall_status = "FEW_FIXES_VERIFIED"
        
        self.results["verification_summary"] = {
            "verified_fixes": verified_count,
            "total_fixes": total_fixes,
            "success_rate": f"{success_rate:.1f}%",
            "overall_status": overall_status,
            "recommendations": self._generate_recommendations()
        }
        
        self.results["overall_status"] = overall_status
        
        print(f"\n🎯 修复验证总结:")
        print(f"   - 验证通过: {verified_count}/{total_fixes} ({success_rate:.1f}%)")
        print(f"   - 整体状态: {overall_status}")
    
    def _generate_recommendations(self) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        if not self.results["bug5_fix"].get("verified", False):
            recommendations.append("继续修复Bug5: 检查错误处理器中的统计计算逻辑")
        
        if not self.results["bug6_fix"].get("verified", False):
            recommendations.append("继续修复Bug6: 添加更多WebSocket空指针检查")
        
        if not self.results["problem7_fix"].get("verified", False):
            recommendations.append("继续修复问题7: 确保API配置统一管理")
        
        if len(recommendations) == 0:
            recommendations.append("所有关键修复已验证通过，可以进行系统测试")
        
        return recommendations

def main():
    """主函数"""
    print("🔍 修复效果验证脚本启动")
    print("=" * 50)
    
    try:
        # 创建验证引擎
        engine = FixVerificationEngine()
        
        # 运行验证
        results = engine.run_verification()
        
        # 保存结果
        output_file = engine.project_root / "diagnostic_results" / f"fix_verification_{int(time.time())}.json"
        output_file.parent.mkdir(exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ 验证完成，结果已保存到: {output_file}")
        
        # 返回验证是否成功
        return results["overall_status"] in ["ALL_FIXES_VERIFIED", "MOST_FIXES_VERIFIED"]
        
    except Exception as e:
        print(f"❌ 验证脚本执行失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)