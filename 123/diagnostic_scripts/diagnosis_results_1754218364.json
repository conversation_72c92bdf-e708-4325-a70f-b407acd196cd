{"timestamp": 1754218364.7405536, "critical_bugs": {"bug5": {"status": "FIXED", "severity": "LOW", "message": "未发现sum(len([...]))语法错误"}, "bug6": {"status": "PARTIALLY_FIXED", "severity": "MEDIUM", "null_pointer_risks": [], "fixed_patterns": [{"file": "websocket/ws_client.py", "line_number": 249, "line_content": "await self.ws.close()", "status": "已修复空指针检查"}, {"file": "websocket/ws_client.py", "line_number": 327, "line_content": "await self.ws.close()", "status": "已修复空指针检查"}, {"file": "websocket/ws_client.py", "line_number": 332, "line_content": "await self.ws.close()", "status": "已修复空指针检查"}, {"file": "websocket/ws_client.py", "line_number": 397, "line_content": "await self.ws.close()", "status": "已修复空指针检查"}, {"file": "websocket/ws_client.py", "line_number": 709, "line_content": "await self.ws.close()", "status": "已修复空指针检查"}, {"file": "websocket/ws_client.py", "line_number": 738, "line_content": "await self.ws.close()", "status": "已修复空指针检查"}, {"file": "websocket/ws_client.py", "line_number": 878, "line_content": "await self.ws.close()", "status": "已修复空指针检查"}, {"file": "websocket/okx_ws.py", "line_number": 100, "line_content": "await self.ws.close()", "status": "已修复空指针检查"}, {"file": "websocket/okx_ws.py", "line_number": 529, "line_content": "await self.ws.close()", "status": "已修复空指针检查"}], "risks_count": 0, "fixed_count": 9, "message": "发现0处空指针风险，9处已修复"}, "bug7": {"status": "PARTIALLY_IMPLEMENTED", "severity": "MEDIUM", "method_status": {"get_synced_timestamp": "EXISTS", "sync_exchange_time": "MISSING", "get_sync_status": "EXISTS", "initialize_all_timestamp_processors": "EXISTS"}, "missing_methods": ["sync_exchange_time"], "message": "时间戳处理器实现状态: 3/4个方法已实现"}}, "system_problems": {"problem7": {"status": "INCONSISTENT", "severity": "HIGH", "optimizer_config": 1, "okx_config": 3, "message": "API限速配置不一致: 优化器1次/秒 vs OKX3次/秒"}, "problem8": {"status": "NO_FAILURES_FOUND", "severity": "MEDIUM", "total_failures": 0, "failed_symbols": [], "failure_details": [], "message": "发现0次合约信息获取失败，涉及0个交易对"}, "problem9": {"status": "PARTIALLY_IMPLEMENTED", "severity": "MEDIUM", "validation_tools": [{"file": "core/trading_system_initializer.py", "patterns_found": ["def.*?check.*?symbols"], "has_validation": true}], "tools_count": 1, "message": "发现1个配置验证工具"}}, "architecture_issues": {"websocket_duplication": {"status": "CONFIRMED", "severity": "HIGH", "existing_managers": [{"file": "websocket/ws_manager.py", "class": "WebSocketManager", "responsibilities": ["连接管理", "数据聚合", "状态监控"]}, {"file": "websocket/unified_connection_pool_manager.py", "class": "UnifiedConnectionPoolManager", "responsibilities": ["连接池管理", "重连策略", "故障转移"]}, {"file": "websocket/unified_websocket_pool_manager.py", "class": "UnifiedWebSocketPoolManager", "responsibilities": ["WebSocket池管理", "客户端注册", "健康检查"]}], "managers_count": 3, "overlapping_responsibilities": [], "message": "发现3个WebSocket管理模块，存在职责重叠"}, "api_rate_limit_duplication": {"status": "CONFIRMED", "severity": "MEDIUM", "implementations": [{"file": "core/api_call_optimizer.py", "patterns_found": ["rate_limit =", "rate_limit =", "rate_limit =", "rate_limit =", "def _handle_rate_limit", "def test_rate_limit", "def _rate_limit", "def rate_limit", "def _execute_rate_limit"], "implementation_count": 9}, {"file": "exchanges/okx_exchange.py", "patterns_found": ["rate_limit =", "rate_limit =", "rate_limit =", "default_leverage}倍, API限制={self.rate_limit", "def _rate_limit"], "implementation_count": 5}, {"file": "exchanges/gate_exchange.py", "patterns_found": ["rate_limit =", "def _rate_limit", "def _rate_limit"], "implementation_count": 3}, {"file": "exchanges/bybit_exchange.py", "patterns_found": ["rate_limit =", "def _rate_limit", "def _rate_limit"], "implementation_count": 3}, {"file": "exchanges/exchanges_base.py", "patterns_found": ["rate_limit =", "rate_limit =", "rate_limit =", "default_rate_limit", "default_rate_limit", "def _rate_limit"], "implementation_count": 6}], "implementations_count": 5, "message": "发现5个文件包含限速实现"}}, "consistency_check": {"overall_score": 71.42857142857143, "total_checks": 7, "consistent_checks": 5, "details": {"websocket_clients": {"checks": {"base_class_inheritance": {"consistent": true, "details": {"okx": true, "gate": true, "bybit": true}}, "run": {"consistent": true, "details": {"okx": true, "gate": true, "bybit": true}}, "close": {"consistent": false, "details": {"okx": true, "gate": false, "bybit": false}}, "send_heartbeat": {"consistent": true, "details": {"okx": true, "gate": true, "bybit": true}}, "handle_message": {"consistent": true, "details": {"okx": true, "gate": true, "bybit": true}}}, "summary": "WebSocket客户端一致性检查完成"}, "exchange_classes": {"checks": {"base_class_inheritance": {"consistent": true, "details": {"okx": true, "gate": true, "bybit": true}}}, "summary": "交易所类一致性检查完成"}, "error_handling": {"checks": {"unified_error_handler": {"consistent": false, "details": {"okx": false, "gate": false, "bybit": false}}}, "summary": "错误处理一致性检查完成"}}, "status": "NEEDS_IMPROVEMENT"}, "summary": {"health_status": "FAIR", "total_issues": 3, "critical_bugs": 0, "system_problems": 1, "architecture_issues": 2, "consistency_score": 71.42857142857143, "recommendations": ["统一API限速配置，消除不一致性(问题7)", "整合重复的WebSocket管理模块，消除职责重叠", "提高三交易所一致性，统一接口和错误处理逻辑"]}}